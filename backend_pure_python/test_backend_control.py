#!/usr/bin/env python3
"""
直接测试后端API的用餐控制逻辑
"""
import requests
import json

def test_backend_api():
    """直接测试后端API"""
    base_url = "http://localhost:8001"
    
    print("🔍 测试后端API用餐控制逻辑...")
    
    # 测试未开始用餐的包厢发送非用餐开始指令
    test_room = "test_new_room"
    
    print(f"\n🔍 测试包厢 {test_room} (应该未开始用餐)")
    
    # 直接调用API，不需要登录（测试权限控制）
    command_data = {
        "room_number": test_room,
        "action_type": "clean_table",
        "action_content": "测试清理"
    }
    
    print(f"🔍 尝试发送清理指令到未开始用餐的包厢...")
    response = requests.post(f"{base_url}/waiter/send-command", 
                           json=command_data)
    
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 401:
        print("✅ 正确返回401 Unauthorized（未登录）")
    elif response.status_code == 403:
        print("✅ 正确返回403 Forbidden（权限不足）")
    elif response.status_code == 400:
        try:
            error_data = response.json()
            if "尚未开始用餐" in error_data.get('detail', ''):
                print("✅ 正确拒绝：包厢尚未开始用餐")
            else:
                print(f"❓ 400错误但原因不明: {error_data}")
        except:
            print(f"❓ 400错误但无法解析响应: {response.text}")
    else:
        print(f"❌ 意外的状态码: {response.status_code}")

if __name__ == "__main__":
    test_backend_api()
