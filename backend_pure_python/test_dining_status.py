#!/usr/bin/env python3
"""
测试用餐状态检查功能
"""

import requests
import json

# 服务器地址
BASE_URL = "http://localhost:8001"

def test_dining_status():
    """测试用餐状态检查"""
    
    # 创建会话
    session = requests.Session()
    
    print("🔍 测试用餐状态检查功能")
    print("=" * 50)
    
    # 1. 先用管理员登录
    print("1. 管理员登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        print("✅ 管理员登录成功")
    else:
        print(f"❌ 管理员登录失败: {response.status_code}")
        return
    
    # 2. 检查包厢1的用餐状态（管理员视角）
    print("\n2. 检查包厢1用餐状态（管理员视角）...")
    response = session.get(f"{BASE_URL}/api/room-dining-status/1")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 管理员检查结果: {data}")
    else:
        print(f"❌ 管理员检查失败: {response.status_code}")
    
    # 3. 先授权服务员ID=5到包厢1
    print("\n3. 授权服务员到包厢1...")
    auth_data = {
        "assigned_tables": "1"
    }
    response = session.post(f"{BASE_URL}/users/5/authorize", json=auth_data)
    if response.status_code == 200:
        print("✅ 服务员授权成功")
    else:
        print(f"❌ 服务员授权失败: {response.status_code}")

    # 4. 切换到服务员登录
    print("\n4. 服务员登录...")
    # 先退出管理员
    session.get(f"{BASE_URL}/logout")

    # 使用实际存在的服务员（用户名为"3"）
    # 密码可能是默认的
    login_data = {
        "username": "3",
        "password": "123456"  # 尝试常见密码
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        print("✅ 服务员登录成功")

        # 检查登录状态
        print("\n4.1 检查服务员登录状态...")
        response = session.get(f"{BASE_URL}/api/check-auth-status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务员认证状态: {data}")
        else:
            print(f"❌ 服务员认证检查失败: {response.status_code}")

        # 5. 检查包厢1的用餐状态（服务员视角）
        print("\n5. 检查包厢1用餐状态（服务员视角）...")
        response = session.get(f"{BASE_URL}/waiter/check-dining-status/1")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务员检查结果: {data}")
        elif response.status_code == 403:
            print(f"❌ 服务员权限不足: {response.text}")
        else:
            print(f"❌ 服务员检查失败: {response.status_code} - {response.text}")
    else:
        print(f"❌ 服务员登录失败: {response.status_code}")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    test_dining_status()
