#!/usr/bin/env python3
"""
添加厨师长用户
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models.user import User, UserRole, UserStatus
from werkzeug.security import generate_password_hash

# 数据库配置
DATABASE_URL = "sqlite:///../paocai.db"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def add_chef_manager():
    """添加厨师长用户"""
    db = SessionLocal()
    try:
        # 检查是否已存在厨师长用户
        existing_chef = db.query(User).filter(
            User.role == UserRole.CHEF_MANAGER
        ).first()
        
        if existing_chef:
            print(f"厨师长用户已存在: {existing_chef.username}")
            return
        
        # 创建厨师长用户
        chef_manager = User(
            username="chef_manager",
            hashed_password=generate_password_hash("chef123"),
            full_name="厨师长",
            role=UserRole.CHEF_MANAGER,
            status=UserStatus.ACTIVE,
            is_authorized=True
        )
        
        db.add(chef_manager)
        db.commit()
        
        print(f"✅ 成功创建厨师长用户: {chef_manager.username}")
        print(f"   用户名: chef_manager")
        print(f"   密码: chef123")
        print(f"   姓名: {chef_manager.full_name}")
        print(f"   角色: {chef_manager.role.value}")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 创建厨师长用户失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    add_chef_manager()
