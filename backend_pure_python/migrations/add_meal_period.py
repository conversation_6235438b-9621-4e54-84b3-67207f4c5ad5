"""
添加用餐时段字段到订单表
"""

import sqlite3
import os
from pathlib import Path

def run_migration():
    """执行数据库迁移"""
    
    # 获取数据库文件路径
    db_path = Path(__file__).parent.parent / "paocai.db"
    
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("🔍 检查是否已存在meal_period字段...")
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(orders)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'meal_period' in columns:
            print("✅ meal_period字段已存在，跳过迁移")
            conn.close()
            return True
        
        print("📝 开始添加meal_period字段...")
        
        # 添加meal_period字段
        cursor.execute("""
            ALTER TABLE orders 
            ADD COLUMN meal_period VARCHAR(20) DEFAULT 'dinner'
        """)
        
        # 更新现有记录的默认值
        cursor.execute("""
            UPDATE orders 
            SET meal_period = 'dinner' 
            WHERE meal_period IS NULL
        """)
        
        # 提交更改
        conn.commit()
        
        print("✅ 成功添加meal_period字段")
        print("📊 字段详情:")
        print("   - 字段名: meal_period")
        print("   - 类型: VARCHAR(20)")
        print("   - 默认值: 'dinner'")
        print("   - 可选值: 'breakfast', 'lunch', 'dinner'")
        
        # 验证字段添加成功
        cursor.execute("PRAGMA table_info(orders)")
        columns = cursor.fetchall()
        
        meal_period_column = None
        for column in columns:
            if column[1] == 'meal_period':
                meal_period_column = column
                break
        
        if meal_period_column:
            print(f"✅ 验证成功: {meal_period_column}")
        else:
            print("❌ 验证失败: 未找到meal_period字段")
            return False
        
        # 检查现有订单数量
        cursor.execute("SELECT COUNT(*) FROM orders")
        order_count = cursor.fetchone()[0]
        print(f"📋 已更新 {order_count} 个现有订单的用餐时段为默认值(晚餐)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def rollback_migration():
    """回滚迁移（SQLite不支持DROP COLUMN，需要重建表）"""
    
    db_path = Path(__file__).parent.parent / "paocai.db"
    
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("⚠️  警告: SQLite不支持直接删除列")
        print("💡 如需回滚，请考虑以下选项:")
        print("   1. 恢复数据库备份")
        print("   2. 重新初始化数据库")
        print("   3. 手动处理数据")
        
        conn.close()
        return False
        
    except Exception as e:
        print(f"❌ 回滚失败: {str(e)}")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        print("🔄 执行回滚迁移...")
        rollback_migration()
    else:
        print("🚀 执行数据库迁移...")
        success = run_migration()
        if success:
            print("🎉 迁移完成!")
        else:
            print("💥 迁移失败!")
            sys.exit(1)
