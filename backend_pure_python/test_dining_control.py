#!/usr/bin/env python3
"""
测试用餐控制逻辑
"""
import requests
import json

def test_dining_control():
    """测试用餐控制逻辑"""
    base_url = "http://localhost:8001"
    
    # 使用服务员账号登录
    login_data = {
        "username": "3",  # 数据库中的服务员用户名
        "password": "123456"  # 尝试默认密码
    }
    
    print("🔍 测试用餐控制逻辑...")
    session = requests.Session()
    
    # 登录
    login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
    if login_response.status_code == 302:
        print("✅ 服务员登录成功")
    else:
        print(f"❌ 服务员登录失败: {login_response.status_code}")
        print(f"响应内容: {login_response.text}")
        return
    
    # 测试包厢用餐状态
    test_room = "test2"
    print(f"\n🔍 测试包厢 {test_room} 的用餐状态...")
    
    # 检查用餐状态
    dining_status_response = session.get(f"{base_url}/waiter/check-dining-status/{test_room}")
    if dining_status_response.status_code == 200:
        status_data = dining_status_response.json()
        print(f"用餐状态: {json.dumps(status_data, indent=2, ensure_ascii=False)}")
        
        if not status_data.get('dining_started', False):
            print(f"✅ 包厢 {test_room} 尚未开始用餐")
            
            # 尝试发送非用餐开始指令（应该被拒绝）
            print(f"\n🔍 尝试发送清理桌面指令到未开始用餐的包厢...")
            command_data = {
                "room_number": test_room,
                "action_type": "clean_table",
                "action_content": ""
            }
            
            command_response = session.post(f"{base_url}/waiter/send-command", 
                                          json=command_data)
            print(f"指令发送状态码: {command_response.status_code}")
            
            if command_response.status_code == 400:
                error_data = command_response.json()
                print(f"✅ 指令被正确拒绝: {error_data.get('detail', '未知错误')}")
            else:
                print(f"❌ 指令应该被拒绝但没有被拒绝")
                print(f"响应内容: {command_response.text}")
            
            # 发送用餐开始指令（应该成功）
            print(f"\n🔍 发送用餐开始指令...")
            start_command_data = {
                "room_number": test_room,
                "action_type": "dining_start",
                "action_content": "测试用餐开始"
            }
            
            start_response = session.post(f"{base_url}/waiter/send-command", 
                                        json=start_command_data)
            print(f"用餐开始指令状态码: {start_response.status_code}")
            
            if start_response.status_code == 200:
                print(f"✅ 用餐开始指令发送成功")
                
                # 再次检查用餐状态
                print(f"\n🔍 再次检查用餐状态...")
                dining_status_response2 = session.get(f"{base_url}/waiter/check-dining-status/{test_room}")
                if dining_status_response2.status_code == 200:
                    status_data2 = dining_status_response2.json()
                    print(f"新的用餐状态: {json.dumps(status_data2, indent=2, ensure_ascii=False)}")
                    
                    if status_data2.get('dining_started', False):
                        print(f"✅ 包厢 {test_room} 已开始用餐")
                        
                        # 现在尝试发送其他指令（应该成功）
                        print(f"\n🔍 尝试发送清理桌面指令到已开始用餐的包厢...")
                        command_response2 = session.post(f"{base_url}/waiter/send-command", 
                                                        json=command_data)
                        print(f"指令发送状态码: {command_response2.status_code}")
                        
                        if command_response2.status_code == 200:
                            print(f"✅ 指令发送成功（用餐已开始）")
                        else:
                            print(f"❌ 指令发送失败: {command_response2.text}")
                    else:
                        print(f"❌ 用餐状态未正确更新")
                else:
                    print(f"❌ 无法获取用餐状态: {dining_status_response2.status_code}")
            else:
                print(f"❌ 用餐开始指令发送失败: {start_response.text}")
        else:
            print(f"ℹ️ 包厢 {test_room} 已经开始用餐")
    else:
        print(f"❌ 无法获取用餐状态: {dining_status_response.status_code}")

if __name__ == "__main__":
    test_dining_control()
