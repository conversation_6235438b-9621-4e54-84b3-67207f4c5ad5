#!/usr/bin/env python3
"""
下载前端资源到本地
"""
import os
import urllib.request
import urllib.error
from pathlib import Path

def download_file(url, local_path):
    """下载文件到本地路径"""
    try:
        print(f"正在下载: {url}")
        urllib.request.urlretrieve(url, local_path)
        print(f"下载完成: {local_path}")
        return True
    except urllib.error.URLError as e:
        print(f"下载失败 {url}: {e}")
        return False
    except Exception as e:
        print(f"下载出错 {url}: {e}")
        return False

def main():
    # 确保目录存在
    os.makedirs("static/css", exist_ok=True)
    os.makedirs("static/js", exist_ok=True)
    os.makedirs("static/fonts", exist_ok=True)
    
    # 要下载的资源列表
    resources = [
        # Bootstrap CSS
        {
            "url": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css",
            "path": "static/css/bootstrap.min.css"
        },
        # Bootstrap JS
        {
            "url": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js",
            "path": "static/js/bootstrap.bundle.min.js"
        },
        # Bootstrap Icons CSS
        {
            "url": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css",
            "path": "static/css/bootstrap-icons.css"
        },
        # Bootstrap Icons 字体文件
        {
            "url": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff",
            "path": "static/fonts/bootstrap-icons.woff"
        },
        {
            "url": "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff2",
            "path": "static/fonts/bootstrap-icons.woff2"
        }
    ]
    
    success_count = 0
    total_count = len(resources)
    
    for resource in resources:
        if download_file(resource["url"], resource["path"]):
            success_count += 1
    
    print(f"\n下载完成: {success_count}/{total_count} 个文件成功下载")
    
    if success_count == total_count:
        print("✅ 所有资源下载成功！")
    else:
        print("⚠️ 部分资源下载失败，请检查网络连接")

if __name__ == "__main__":
    main()
