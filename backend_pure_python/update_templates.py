#!/usr/bin/env python3
"""
批量更新模板文件中的外部CDN链接为本地路径
"""
import os
import re
from pathlib import Path

def update_template_file(file_path):
    """更新单个模板文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换Bootstrap CSS链接
        content = re.sub(
            r'https://cdn\.jsdelivr\.net/npm/bootstrap@[\d\.]+/dist/css/bootstrap\.min\.css',
            '/static/css/bootstrap.min.css',
            content
        )
        
        # 替换Bootstrap JS链接
        content = re.sub(
            r'https://cdn\.jsdelivr\.net/npm/bootstrap@[\d\.]+/dist/js/bootstrap\.bundle\.min\.js',
            '/static/js/bootstrap.bundle.min.js',
            content
        )
        
        # 替换Bootstrap Icons链接
        content = re.sub(
            r'https://cdn\.jsdelivr\.net/npm/bootstrap-icons@[\d\.]+/font/bootstrap-icons\.css',
            '/static/css/bootstrap-icons.css',
            content
        )
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 更新完成: {file_path}")
            return True
        else:
            print(f"⏭️ 无需更新: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 更新失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    templates_dir = Path("templates")
    
    if not templates_dir.exists():
        print("❌ templates目录不存在")
        return
    
    # 获取所有HTML文件
    html_files = list(templates_dir.glob("*.html"))
    
    if not html_files:
        print("❌ 未找到HTML模板文件")
        return
    
    print(f"📋 找到 {len(html_files)} 个HTML模板文件")
    
    updated_count = 0
    for html_file in html_files:
        if update_template_file(html_file):
            updated_count += 1
    
    print(f"\n🎉 批量更新完成: {updated_count}/{len(html_files)} 个文件已更新")

if __name__ == "__main__":
    main()
