#!/usr/bin/env python3
"""
验证暨阳湖大酒店传菜管理系统的浅色主题改造效果
"""
import os
import re
import requests
from pathlib import Path

def check_dark_mode_removal():
    """检查深色模式是否已移除"""
    print("🌞 检查深色模式移除...")
    
    css_files = [
        "static/css/jiyang-enhanced.css",
        "static/css/waiter-mobile.css"
    ]
    
    dark_mode_found = False
    for file_path in css_files:
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否还有深色模式相关代码
        dark_patterns = [
            r'@media\s*\(\s*prefers-color-scheme:\s*dark\s*\)',
            r'background.*#1a1a1a',
            r'background.*#2d2d2d',
            r'background.*#3d3d3d',
            r'color.*#f8f9fa.*dark'
        ]
        
        for pattern in dark_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                print(f"❌ {file_path} 仍包含深色模式代码: {pattern}")
                dark_mode_found = True
                break
        
        if not dark_mode_found:
            print(f"✅ {file_path} 深色模式已移除")
    
    return not dark_mode_found

def check_light_theme_colors():
    """检查浅色主题颜色"""
    print("\n🎨 检查浅色主题颜色...")
    
    enhanced_css_path = "static/css/jiyang-enhanced.css"
    if not os.path.exists(enhanced_css_path):
        print("❌ 增强样式文件不存在")
        return False
        
    with open(enhanced_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查浅色主题必需的颜色
    light_colors = [
        "#ffffff",  # 白色背景
        "#f8f9fa",  # 浅灰背景
        "#f5f6fa",  # 更浅的背景
        "#e9ecef",  # 边框颜色
        "var(--jiyang-dark)"  # 深色文字
    ]
    
    for color in light_colors:
        if color in content:
            print(f"✅ {color}")
        else:
            print(f"❌ 缺少浅色主题颜色: {color}")
            return False
    
    return True

def check_kitchen_display_exception():
    """检查厨房大屏是否保持黑色背景"""
    print("\n🖥️ 检查厨房大屏黑色背景...")
    
    kitchen_template = "templates/kitchen_display_new.html"
    if not os.path.exists(kitchen_template):
        print("❌ 厨房大屏模板不存在")
        return False
        
    with open(kitchen_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否保持黑色背景
    if "background-color: #000000" in content or "background: #000" in content:
        print("✅ 厨房大屏保持黑色背景")
        return True
    else:
        print("❌ 厨房大屏缺少黑色背景")
        return False

def check_waiter_mobile_theme():
    """检查服务员移动端浅色主题"""
    print("\n📱 检查服务员移动端浅色主题...")
    
    mobile_css_path = "static/css/waiter-mobile.css"
    if not os.path.exists(mobile_css_path):
        print("❌ 移动端样式文件不存在")
        return False
        
    with open(mobile_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查浅色主题元素
    light_elements = [
        "background: #ffffff",
        "color: var(--jiyang-dark)",
        "border: 1px solid #e9ecef",
        "background: linear-gradient(135deg, #f8f9fa"
    ]
    
    for element in light_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少浅色主题元素: {element}")
            return False
    
    return True

def check_dashboard_layout():
    """检查工作台布局优化"""
    print("\n📊 检查工作台布局优化...")
    
    dashboard_template = "templates/dashboard.html"
    if not os.path.exists(dashboard_template):
        print("❌ 工作台模板不存在")
        return False
        
    with open(dashboard_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查快速操作是否移到上方
    quick_actions_index = content.find("快速操作")
    recent_orders_index = content.find("最近订单")
    
    if quick_actions_index < recent_orders_index and quick_actions_index != -1:
        print("✅ 快速操作已移到最近订单上方")
    else:
        print("❌ 快速操作布局未优化")
        return False
    
    # 检查订单状态中文显示
    chinese_status = [
        "草稿", "已确认", "进行中", "已完成", "已取消", "待开始", "上菜中"
    ]
    
    status_found = 0
    for status in chinese_status:
        if status in content:
            status_found += 1
    
    if status_found >= 5:
        print(f"✅ 订单状态中文显示完整 ({status_found}/7)")
    else:
        print(f"❌ 订单状态中文显示不完整 ({status_found}/7)")
        return False
    
    return True

def check_server_response():
    """检查服务器浅色主题响应"""
    print("\n🌐 检查服务器浅色主题响应...")
    
    try:
        # 检查登录页面
        response = requests.get("http://localhost:8001/login", timeout=5)
        if response.status_code == 200:
            if "jiyang-enhanced.css" in response.text:
                print("✅ 登录页面包含增强样式")
            else:
                print("❌ 登录页面未包含增强样式")
                return False
        else:
            print(f"❌ 登录页面访问失败: {response.status_code}")
            return False
        
        # 检查CSS文件是否包含浅色主题
        response = requests.get("http://localhost:8001/static/css/jiyang-enhanced.css", timeout=5)
        if response.status_code == 200:
            css_content = response.text
            if "#ffffff" in css_content and "#f8f9fa" in css_content:
                print("✅ CSS文件包含浅色主题颜色")
            else:
                print("❌ CSS文件缺少浅色主题颜色")
                return False
            
            # 检查是否移除了深色模式
            if "prefers-color-scheme: dark" not in css_content:
                print("✅ 深色模式已从CSS中移除")
            else:
                print("❌ CSS中仍包含深色模式")
                return False
        else:
            print(f"❌ CSS文件访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    return True

def check_accessibility():
    """检查可访问性"""
    print("\n♿ 检查可访问性...")
    
    enhanced_css_path = "static/css/jiyang-enhanced.css"
    with open(enhanced_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否保留了减少动画的支持
    if "prefers-reduced-motion: reduce" in content:
        print("✅ 保留减少动画偏好支持")
    else:
        print("❌ 缺少减少动画偏好支持")
        return False
    
    # 检查对比度相关设置
    contrast_elements = [
        "var(--jiyang-dark)",  # 深色文字
        "color: white",        # 白色文字（在深色背景上）
        "border: 2px solid"    # 明显边框
    ]
    
    for element in contrast_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少对比度元素: {element}")
    
    return True

def main():
    """主函数"""
    print("🌞 暨阳湖大酒店传菜管理系统浅色主题验证")
    print("=" * 60)
    
    checks = [
        ("深色模式移除", check_dark_mode_removal),
        ("浅色主题颜色", check_light_theme_colors),
        ("厨房大屏例外", check_kitchen_display_exception),
        ("移动端浅色主题", check_waiter_mobile_theme),
        ("工作台布局优化", check_dashboard_layout),
        ("可访问性", check_accessibility),
        ("服务器响应", check_server_response)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}检查:")
        try:
            if check_func():
                passed += 1
            else:
                print(f"❌ {check_name}检查失败")
        except Exception as e:
            print(f"❌ {check_name}检查出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎉 验证完成: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("✅ 浅色主题改造全面完成！")
        print("\n🌟 改造亮点:")
        print("   • 统一浅色主题，视觉清爽")
        print("   • 厨房大屏保持黑色背景")
        print("   • 工作台布局优化合理")
        print("   • 移动端浅色主题友好")
        print("   • 订单状态中文显示")
        print("   • 保持完全本地化")
        return True
    else:
        print("❌ 浅色主题改造仍需完善")
        return False

if __name__ == "__main__":
    main()
