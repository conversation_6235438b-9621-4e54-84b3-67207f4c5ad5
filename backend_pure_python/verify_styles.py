#!/usr/bin/env python3
"""
验证暨阳湖大酒店传菜管理系统的样式优化效果
"""
import os
import re
import requests
from pathlib import Path

def check_enhanced_styles():
    """检查增强样式文件"""
    print("🎨 检查增强样式文件...")
    
    style_files = [
        "static/css/jiyang-enhanced.css",
        "static/css/waiter-mobile.css"
    ]
    
    for file_path in style_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({file_size} bytes)")
        else:
            print(f"❌ 缺少文件: {file_path}")
            return False
    
    return True

def check_template_updates():
    """检查模板文件是否包含增强样式"""
    print("\n🔍 检查模板文件样式引用...")
    
    templates_to_check = [
        "templates/base.html",
        "templates/login.html", 
        "templates/waiter_menu.html",
        "templates/kitchen_display_new.html"
    ]
    
    for template_path in templates_to_check:
        if not os.path.exists(template_path):
            print(f"❌ 模板文件不存在: {template_path}")
            continue
            
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        has_enhanced = "jiyang-enhanced.css" in content
        has_bootstrap = "bootstrap.min.css" in content
        has_icons = "bootstrap-icons.css" in content
        
        print(f"📄 {template_path}:")
        print(f"   Bootstrap CSS: {'✅' if has_bootstrap else '❌'}")
        print(f"   Bootstrap Icons: {'✅' if has_icons else '❌'}")
        print(f"   增强样式: {'✅' if has_enhanced else '❌'}")
        
        if template_path == "templates/waiter_menu.html":
            has_mobile = "waiter-mobile.css" in content
            print(f"   移动端样式: {'✅' if has_mobile else '❌'}")
    
    return True

def check_css_variables():
    """检查CSS变量定义"""
    print("\n🎯 检查CSS变量定义...")
    
    enhanced_css_path = "static/css/jiyang-enhanced.css"
    if not os.path.exists(enhanced_css_path):
        print("❌ 增强样式文件不存在")
        return False
        
    with open(enhanced_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_variables = [
        "--jiyang-primary",
        "--jiyang-secondary", 
        "--sidebar-width",
        "--navbar-height",
        "--border-radius",
        "--box-shadow",
        "--transition"
    ]
    
    for var in required_variables:
        if var in content:
            print(f"✅ {var}")
        else:
            print(f"❌ 缺少变量: {var}")
            return False
    
    return True

def check_responsive_design():
    """检查响应式设计"""
    print("\n📱 检查响应式设计...")
    
    enhanced_css_path = "static/css/jiyang-enhanced.css"
    with open(enhanced_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    media_queries = [
        "@media (max-width: 768px)",
        "@media (min-width: 769px) and (max-width: 1024px)",
        "@media (prefers-reduced-motion: reduce)",
        "@media (prefers-color-scheme: dark)"
    ]
    
    for query in media_queries:
        if query in content:
            print(f"✅ {query}")
        else:
            print(f"❌ 缺少媒体查询: {query}")
    
    return True

def check_server_response():
    """检查服务器样式响应"""
    print("\n🌐 检查服务器样式响应...")
    
    try:
        # 检查增强样式文件
        response = requests.get("http://localhost:8001/static/css/jiyang-enhanced.css", timeout=5)
        if response.status_code == 200:
            print("✅ 增强样式文件正常访问")
            print(f"   文件大小: {len(response.content)} bytes")
        else:
            print(f"❌ 增强样式文件访问失败: {response.status_code}")
            return False
        
        # 检查移动端样式文件
        response = requests.get("http://localhost:8001/static/css/waiter-mobile.css", timeout=5)
        if response.status_code == 200:
            print("✅ 移动端样式文件正常访问")
        else:
            print(f"❌ 移动端样式文件访问失败: {response.status_code}")
            return False
        
        # 检查登录页面是否包含增强样式
        response = requests.get("http://localhost:8001/login", timeout=5)
        if response.status_code == 200:
            if "jiyang-enhanced.css" in response.text:
                print("✅ 登录页面包含增强样式")
            else:
                print("❌ 登录页面未包含增强样式")
                return False
        else:
            print(f"❌ 登录页面访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    return True

def check_mobile_optimization():
    """检查移动端优化"""
    print("\n📲 检查移动端优化...")
    
    mobile_css_path = "static/css/waiter-mobile.css"
    if not os.path.exists(mobile_css_path):
        print("❌ 移动端样式文件不存在")
        return False
        
    with open(mobile_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    mobile_features = [
        "min-height: 44px",  # 触摸友好按钮
        "font-size: 16px",   # 防止iOS缩放
        "user-select: none", # 防止文本选择
        "-webkit-tap-highlight-color", # 触摸反馈
        "env(safe-area-inset-bottom)", # 安全区域
        "@media screen and (orientation: landscape)" # 横屏适配
    ]
    
    for feature in mobile_features:
        if feature in content:
            print(f"✅ {feature}")
        else:
            print(f"❌ 缺少移动端特性: {feature}")
    
    return True

def check_color_scheme():
    """检查颜色方案"""
    print("\n🎨 检查颜色方案...")
    
    enhanced_css_path = "static/css/jiyang-enhanced.css"
    with open(enhanced_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查暨阳湖主题色
    if "#f2750a" in content and "#e35d05" in content:
        print("✅ 暨阳湖主题色正确设置")
    else:
        print("❌ 暨阳湖主题色设置错误")
        return False
    
    # 检查状态颜色
    status_colors = [
        "table-status-available",
        "table-status-occupied", 
        "table-status-reserved",
        "order-status-confirmed",
        "dish-category"
    ]
    
    for status in status_colors:
        if status in content:
            print(f"✅ {status}")
        else:
            print(f"❌ 缺少状态样式: {status}")
    
    return True

def main():
    """主函数"""
    print("🎯 暨阳湖大酒店传菜管理系统样式优化验证")
    print("=" * 60)
    
    checks = [
        ("增强样式文件", check_enhanced_styles),
        ("模板文件更新", check_template_updates),
        ("CSS变量定义", check_css_variables),
        ("响应式设计", check_responsive_design),
        ("移动端优化", check_mobile_optimization),
        ("颜色方案", check_color_scheme),
        ("服务器响应", check_server_response)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}检查:")
        try:
            if check_func():
                passed += 1
            else:
                print(f"❌ {check_name}检查失败")
        except Exception as e:
            print(f"❌ {check_name}检查出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎉 验证完成: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("✅ 样式优化全面完成，界面效果优秀！")
        print("\n🌟 优化亮点:")
        print("   • 暨阳湖主题色完美应用")
        print("   • 响应式设计适配多设备")
        print("   • 移动端触摸优化")
        print("   • 现代化视觉效果")
        print("   • 完全本地化资源")
        return True
    else:
        print("❌ 样式优化仍需完善")
        return False

if __name__ == "__main__":
    main()
