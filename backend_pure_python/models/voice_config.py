from sqlalchemy import Column, Integer, String, Boolean, Float
from core.database import Base


class VoiceConfig(Base):
    """语音播报配置模型"""
    __tablename__ = "voice_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    config_key = Column(String(50), unique=True, index=True, nullable=False, comment="配置键")
    config_value = Column(String(100), nullable=False, comment="配置值")
    description = Column(String(200), nullable=True, comment="配置描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    def __repr__(self):
        return f"<VoiceConfig(key='{self.config_key}', value='{self.config_value}')>"


# 默认语音播报配置
DEFAULT_VOICE_CONFIGS = [
    {
        "config_key": "voice_enabled",
        "config_value": "true",
        "description": "是否启用语音播报"
    },
    {
        "config_key": "voice_repeat_count",
        "config_value": "2",
        "description": "语音播报重复次数（1-5次）"
    },
    {
        "config_key": "voice_repeat_interval",
        "config_value": "3",
        "description": "语音播报间隔时间（1-10秒）"
    },
    {
        "config_key": "voice_rate",
        "config_value": "0.8",
        "description": "语音播报语速（0.5-2.0）"
    },
    {
        "config_key": "voice_volume",
        "config_value": "1.0",
        "description": "语音播报音量（0.0-1.0）"
    },
    {
        "config_key": "voice_pitch",
        "config_value": "1.0",
        "description": "语音播报音调（0.5-2.0）"
    }
]
