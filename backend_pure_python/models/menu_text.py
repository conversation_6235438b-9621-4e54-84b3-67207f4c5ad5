from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from core.database import Base


class MenuText(Base):
    """菜单文本模型 - 存储商务中心输入的完整菜单"""
    __tablename__ = "menu_texts"
    
    id = Column(Integer, primary_key=True, index=True, comment="菜单ID")
    room_number = Column(String(50), nullable=False, comment="包厢号")
    guest_count = Column(Integer, nullable=False, comment="客人数量")
    menu_content = Column(Text, nullable=False, comment="菜单内容（每行一个菜品）")
    customer_name = Column(String(100), nullable=True, comment="客户姓名")
    special_requests = Column(Text, nullable=True, comment="特殊要求")
    
    # 状态字段
    is_active = Column(Boolean, default=True, comment="是否有效")
    is_served = Column(Boolean, default=False, comment="是否已上菜")
    
    # 关联字段
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建人ID")
    assigned_waiter = Column(Integer, ForeignKey("users.id"), nullable=True, comment="分配的服务员ID")
    
    # 时间字段
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    served_at = Column(DateTime, nullable=True, comment="上菜时间")
    
    # 关系
    creator = relationship("User", foreign_keys=[created_by])
    waiter = relationship("User", foreign_keys=[assigned_waiter])
    dish_items = relationship("MenuDishItem", back_populates="menu", cascade="all, delete-orphan")
    
    def get_dish_list(self):
        """获取菜品列表"""
        if not self.menu_content:
            return []
        
        dishes = []
        for line in self.menu_content.strip().split('\n'):
            line = line.strip()
            if line:
                dishes.append(line)
        return dishes
    
    def get_dish_count(self):
        """获取菜品数量"""
        return len(self.get_dish_list())


class MenuDishItem(Base):
    """菜单菜品项目 - 每个菜品的状态"""
    __tablename__ = "menu_dish_items"
    
    id = Column(Integer, primary_key=True, index=True, comment="菜品项目ID")
    menu_id = Column(Integer, ForeignKey("menu_texts.id"), nullable=False, comment="菜单ID")
    dish_name = Column(String(200), nullable=False, comment="菜品名称")
    dish_order = Column(Integer, nullable=False, comment="菜品顺序")
    
    # 状态字段
    is_served = Column(Boolean, default=False, comment="是否已上菜")
    is_rushed = Column(Boolean, default=False, comment="是否已催菜")
    is_changed = Column(Boolean, default=False, comment="是否已换菜")
    
    # 操作记录
    served_at = Column(DateTime, nullable=True, comment="上菜时间")
    rushed_at = Column(DateTime, nullable=True, comment="催菜时间")
    changed_at = Column(DateTime, nullable=True, comment="换菜时间")
    
    # 操作人员
    served_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="上菜人员ID")
    rushed_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="催菜人员ID")
    changed_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="换菜人员ID")
    
    # 备注
    rush_note = Column(Text, nullable=True, comment="催菜备注")
    change_note = Column(Text, nullable=True, comment="换菜备注")
    
    # 时间字段
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    menu = relationship("MenuText", back_populates="dish_items")
    served_user = relationship("User", foreign_keys=[served_by])
    rushed_user = relationship("User", foreign_keys=[rushed_by])
    changed_user = relationship("User", foreign_keys=[changed_by])


class WaiterAction(Base):
    """服务员操作记录"""
    __tablename__ = "waiter_actions"
    
    id = Column(Integer, primary_key=True, index=True, comment="操作ID")
    menu_id = Column(Integer, ForeignKey("menu_texts.id"), nullable=False, comment="菜单ID")
    waiter_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="服务员ID")
    action_type = Column(String(50), nullable=False, comment="操作类型")
    action_content = Column(Text, nullable=True, comment="操作内容")
    
    # 时间字段
    created_at = Column(DateTime, server_default=func.now(), comment="操作时间")
    
    # 关系
    menu = relationship("MenuText")
    waiter = relationship("User")
