#!/usr/bin/env python3
"""
测试服务员菜单页面的数据传递
"""

from core.database import SessionLocal
from models.user import User
from models.order import Order, OrderItem, OrderStatus
from models.table import Table

def test_waiter_menu_data():
    """测试服务员菜单页面的数据获取逻辑"""
    db = SessionLocal()
    
    try:
        print("=== 测试服务员菜单数据获取 ===")
        
        # 测试用户5
        user = db.query(User).filter(User.username == '5').first()
        if not user:
            print("❌ 用户5不存在")
            return
        
        print(f"✅ 用户5信息:")
        print(f"   用户名: {user.username}")
        print(f"   姓名: {user.full_name}")
        print(f"   角色: {user.role}")
        print(f"   授权状态: {user.is_authorized}")
        print(f"   分配包厢: {user.assigned_tables}")
        
        if not user.assigned_tables:
            print("❌ 用户5没有分配包厢")
            return
        
        # 模拟服务员菜单页面的数据获取逻辑
        assigned_room_orders = {}
        room_info = {}
        
        assigned_rooms = [room.strip() for room in user.assigned_tables.split(',')]
        print(f"\n📋 分配的包厢列表: {assigned_rooms}")
        
        for room_number in assigned_rooms:
            print(f"\n🏠 处理包厢: {room_number}")
            
            # 查找包厢
            table = db.query(Table).filter(Table.number == room_number).first()
            if not table:
                print(f"   ❌ 包厢 {room_number} 不存在")
                continue
            
            print(f"   包厢ID: {table.id}, 状态: {table.status}")
            
            # 获取该包厢的活跃订单
            room_orders = db.query(Order).join(Table).filter(
                Table.number == room_number,
                Order.status.in_([
                    OrderStatus.CONFIRMED,
                    OrderStatus.IN_PROGRESS,
                    OrderStatus.RESERVED,
                    OrderStatus.PENDING_START,
                    OrderStatus.SERVING
                ])
            ).all()
            
            print(f"   活跃订单数: {len(room_orders)}")
            
            room_items = []
            for order in room_orders:
                print(f"     订单ID: {order.id}, 状态: {order.status}")
                order_items = db.query(OrderItem).filter(
                    OrderItem.order_id == order.id
                ).all()
                print(f"       菜品数: {len(order_items)}")
                
                for item in order_items[:5]:  # 只显示前5个菜品
                    print(f"         - {item.dish_name}: {item.status}")
                
                room_items.extend(order_items)
            
            # 保存数据
            assigned_room_orders[room_number] = room_items
            print(f"   最终菜品总数: {len(room_items)}")
            
            # 获取包厢基本信息
            latest_order = room_orders[0] if room_orders else None
            if latest_order:
                room_info[room_number] = {
                    'dining_standard': latest_order.dining_standard or 0,
                    'guest_count': latest_order.guest_count or 0,
                    'order_time': latest_order.created_at.strftime('%H:%M'),
                    'special_requirements': latest_order.special_requirements
                }
                print(f"   包厢信息: {room_info[room_number]}")
        
        print(f"\n📊 最终结果:")
        print(f"   assigned_room_orders 包厢数: {len(assigned_room_orders)}")
        print(f"   room_info 包厢数: {len(room_info)}")
        
        for room, items in assigned_room_orders.items():
            print(f"   {room}: {len(items)} 个菜品")
            if len(items) > 0:
                print(f"     示例菜品: {items[0].dish_name} ({items[0].status})")
        
        # 检查模板渲染条件
        print(f"\n🔍 模板渲染检查:")
        print(f"   assigned_room_orders 是否为空: {not bool(assigned_room_orders)}")
        print(f"   包厢数量: {len(assigned_room_orders)}")
        
        if assigned_room_orders:
            print("   ✅ 应该显示包厢和菜品")
            if len(assigned_room_orders) == 1:
                print("   📱 单包厢模式")
            else:
                print("   📱 多包厢模式（标签页）")
        else:
            print("   ❌ 应该显示'暂无分配的包厢'")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    test_waiter_menu_data()
