#!/usr/bin/env python3
"""
验证系统在离线环境下的完整性
"""
import os
import re
import requests
from pathlib import Path

def check_static_files():
    """检查静态文件是否存在"""
    print("🔍 检查静态文件...")
    
    required_files = [
        "static/css/bootstrap.min.css",
        "static/css/bootstrap-icons.css", 
        "static/js/bootstrap.bundle.min.js",
        "static/fonts/bootstrap-icons.woff",
        "static/fonts/bootstrap-icons.woff2"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    print("✅ 所有静态文件存在")
    return True

def check_templates():
    """检查模板文件中是否还有外部依赖"""
    print("\n🔍 检查模板文件中的外部依赖...")
    
    templates_dir = Path("templates")
    external_deps = []
    
    for html_file in templates_dir.glob("*.html"):
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找外部CDN链接
        cdn_links = re.findall(r'https?://[^\s"\']+', content)
        if cdn_links:
            # 过滤掉localhost链接
            external_links = [link for link in cdn_links if 'localhost' not in link]
            if external_links:
                external_deps.append((html_file.name, external_links))
    
    if external_deps:
        print("❌ 发现外部依赖:")
        for file_name, links in external_deps:
            print(f"  {file_name}: {links}")
        return False
    
    print("✅ 所有模板文件已本地化")
    return True

def check_server_response():
    """检查服务器响应"""
    print("\n🔍 检查服务器响应...")
    
    try:
        # 检查静态资源
        static_urls = [
            "http://localhost:8001/static/css/bootstrap.min.css",
            "http://localhost:8001/static/css/bootstrap-icons.css",
            "http://localhost:8001/static/js/bootstrap.bundle.min.js"
        ]
        
        for url in static_urls:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {url}")
            else:
                print(f"❌ {url} - 状态码: {response.status_code}")
                return False
        
        # 检查登录页面
        response = requests.get("http://localhost:8001/login", timeout=5)
        if response.status_code == 200:
            print("✅ 登录页面正常")
            
            # 检查页面中是否包含本地资源链接
            if "/static/css/bootstrap.min.css" in response.text:
                print("✅ 页面使用本地CSS资源")
            else:
                print("❌ 页面未使用本地CSS资源")
                return False
                
            if "/static/js/bootstrap.bundle.min.js" in response.text:
                print("✅ 页面使用本地JS资源")
            else:
                print("❌ 页面未使用本地JS资源")
                return False
        else:
            print(f"❌ 登录页面访问失败 - 状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    print("✅ 服务器响应正常")
    return True

def check_directory_structure():
    """检查目录结构"""
    print("\n🔍 检查目录结构...")
    
    required_dirs = [
        "static",
        "static/css", 
        "static/js",
        "static/fonts",
        "static/images",
        "templates"
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ 缺少目录: {dir_path}/")
            return False
    
    print("✅ 目录结构完整")
    return True

def main():
    """主函数"""
    print("🎯 暨阳湖大酒店传菜管理系统离线验证")
    print("=" * 50)
    
    checks = [
        ("目录结构", check_directory_structure),
        ("静态文件", check_static_files),
        ("模板文件", check_templates),
        ("服务器响应", check_server_response)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}检查:")
        if check_func():
            passed += 1
        else:
            print(f"❌ {check_name}检查失败")
    
    print("\n" + "=" * 50)
    print(f"🎉 验证完成: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("✅ 系统完全自包含，可在离线环境运行！")
        return True
    else:
        print("❌ 系统仍有外部依赖，需要修复")
        return False

if __name__ == "__main__":
    main()
