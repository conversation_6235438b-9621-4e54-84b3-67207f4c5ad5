#!/usr/bin/env python3
"""
验证暨阳湖大酒店传菜管理系统的服务员操作流程优化
"""
import os
import re
import requests
from pathlib import Path

def check_waiter_css_optimization():
    """检查服务员界面CSS优化"""
    print("🎨 检查服务员界面CSS优化...")
    
    mobile_css_path = "static/css/waiter-mobile.css"
    if not os.path.exists(mobile_css_path):
        print("❌ 移动端样式文件不存在")
        return False
        
    with open(mobile_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查按钮尺寸统一
    required_styles = [
        "min-height: 56px",
        "min-width: 56px", 
        "padding: 16px 24px",
        "font-size: 1.1rem",
        ".action-btn",
        ".disabled-state",
        ".start-dining-btn"
    ]
    
    for style in required_styles:
        if style in content:
            print(f"✅ {style}")
        else:
            print(f"❌ 缺少样式: {style}")
            return False
    
    # 检查禁用状态样式
    if "cursor: not-allowed" in content and "pointer-events: none" in content:
        print("✅ 禁用状态样式完整")
    else:
        print("❌ 禁用状态样式不完整")
        return False
    
    # 检查开始用餐按钮特殊样式
    if "start-dining-btn" in content and "pulse-red" in content:
        print("✅ 开始用餐按钮特殊样式完整")
    else:
        print("❌ 开始用餐按钮特殊样式不完整")
        return False
    
    return True

def check_waiter_template_optimization():
    """检查服务员模板优化"""
    print("\n📱 检查服务员模板优化...")
    
    waiter_template = "templates/waiter_menu.html"
    if not os.path.exists(waiter_template):
        print("❌ 服务员模板不存在")
        return False
        
    with open(waiter_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查强制开始用餐流程
    required_elements = [
        "start-dining-btn",
        "disableCommandButtons",
        "enableCommandButtons",
        "disabled-state",
        "请先点击\"开始用餐\"按钮"
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    # 检查JavaScript函数
    js_functions = [
        "function disableCommandButtons",
        "function enableCommandButtons",
        "function startDining"
    ]
    
    for func in js_functions:
        if func in content:
            print(f"✅ {func}")
        else:
            print(f"❌ 缺少函数: {func}")
            return False
    
    return True

def check_kitchen_display_optimization():
    """检查厨房大屏优化"""
    print("\n🖥️ 检查厨房大屏优化...")
    
    kitchen_template = "templates/kitchen_display_new.html"
    if not os.path.exists(kitchen_template):
        print("❌ 厨房大屏模板不存在")
        return False
        
    with open(kitchen_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查用餐开始消息处理
    required_elements = [
        "dining-start-popup",
        "dining-start-header",
        "dining-start-info",
        "dining-start-message",
        "pulse-green",
        "action_type === 'dining_start'"
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    # 检查语音播报优化
    if "起菜" in content:
        print("✅ 语音播报包含'起菜'内容")
    else:
        print("❌ 语音播报缺少'起菜'内容")
        return False
    
    return True

def check_backend_optimization():
    """检查后端优化"""
    print("\n⚙️ 检查后端优化...")
    
    main_py_path = "main.py"
    if not os.path.exists(main_py_path):
        print("❌ 主程序文件不存在")
        return False
        
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查订单状态查询优化
    required_elements = [
        "'reserved'",
        "'pending_start'", 
        "'serving'",
        "dining_start_message",
        "起菜"
    ]
    
    for element in required_elements:
        if element in content:
            print(f"✅ {element}")
        else:
            print(f"❌ 缺少元素: {element}")
            return False
    
    # 检查厨房端查询优化
    if "包含商务中心创建的订单" in content:
        print("✅ 厨房端查询包含商务中心订单")
    else:
        print("❌ 厨房端查询未优化")
        return False
    
    return True

def check_server_response():
    """检查服务器响应"""
    print("\n🌐 检查服务器响应...")
    
    try:
        # 检查服务员界面 (需要登录，检查登录页面是否包含相关样式)
        response = requests.get("http://localhost:8001/login", timeout=5)
        if response.status_code == 200:
            print("✅ 登录页面正常访问")
        else:
            print(f"❌ 登录页面访问失败: {response.status_code}")
            return False
        
        # 厨房页面需要登录，跳过直接访问检查
        print("✅ 厨房页面需要登录验证（正常）")
        
        # 检查CSS文件
        response = requests.get("http://localhost:8001/static/css/waiter-mobile.css", timeout=5)
        if response.status_code == 200:
            css_content = response.text
            if "start-dining-btn" in css_content and "disabled-state" in css_content:
                print("✅ 移动端CSS包含优化样式")
            else:
                print("❌ 移动端CSS缺少优化样式")
                return False
        else:
            print(f"❌ 移动端CSS访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        return False
    
    return True

def check_button_standardization():
    """检查按钮标准化"""
    print("\n🔘 检查按钮标准化...")
    
    mobile_css_path = "static/css/waiter-mobile.css"
    with open(mobile_css_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查按钮尺寸标准
    button_standards = [
        "min-height: 56px !important",
        "min-width: 56px !important",
        "padding: 16px 24px !important",
        "font-size: 1.1rem !important",
        "margin-bottom: 12px !important"
    ]
    
    for standard in button_standards:
        if standard in content:
            print(f"✅ {standard}")
        else:
            print(f"❌ 缺少标准: {standard}")
            return False
    
    # 检查触摸优化
    touch_optimizations = [
        "transform: scale(0.98)",
        "transition: all 0.3s ease",
        "border-radius: 12px"
    ]
    
    for optimization in touch_optimizations:
        if optimization in content:
            print(f"✅ {optimization}")
        else:
            print(f"❌ 缺少优化: {optimization}")
    
    return True

def main():
    """主函数"""
    print("🎯 暨阳湖大酒店传菜管理系统服务员操作流程优化验证")
    print("=" * 70)
    
    checks = [
        ("服务员CSS优化", check_waiter_css_optimization),
        ("服务员模板优化", check_waiter_template_optimization),
        ("厨房大屏优化", check_kitchen_display_optimization),
        ("后端逻辑优化", check_backend_optimization),
        ("按钮标准化", check_button_standardization),
        ("服务器响应", check_server_response)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}检查:")
        try:
            if check_func():
                passed += 1
            else:
                print(f"❌ {check_name}检查失败")
        except Exception as e:
            print(f"❌ {check_name}检查出错: {e}")
    
    print("\n" + "=" * 70)
    print(f"🎉 验证完成: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("✅ 服务员操作流程优化全面完成！")
        print("\n🌟 优化亮点:")
        print("   • 强制开始用餐流程实现")
        print("   • 按钮尺寸统一标准化")
        print("   • 厨房端消息显示优化")
        print("   • 订单显示问题修复")
        print("   • 移动端触摸体验优化")
        print("   • 语音播报功能增强")
        return True
    else:
        print("❌ 服务员操作流程优化仍需完善")
        return False

if __name__ == "__main__":
    main()
