#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本 - 测试厨房大屏配置API
"""

import urllib.request
import urllib.parse
import urllib.error
import json
import sys
import http.cookiejar

def test_kitchen_display_config_api():
    """测试厨房大屏配置API"""
    base_url = "http://localhost:8001"

    print("🧪 测试厨房大屏配置API...")

    try:
        # 创建cookie处理器
        cookie_jar = http.cookiejar.CookieJar()
        opener = urllib.request.build_opener(urllib.request.HTTPCookieProcessor(cookie_jar))

        # 测试登录
        print("1. 尝试登录...")
        login_data = urllib.parse.urlencode({
            "username": "admin",
            "password": "admin123"
        }).encode('utf-8')

        login_req = urllib.request.Request(
            f"{base_url}/login",
            data=login_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )

        login_response = opener.open(login_req)
        print(f"   登录状态码: {login_response.getcode()}")

        if login_response.getcode() != 200:
            print("❌ 登录失败")
            return False

        print("✅ 登录成功")

        # 测试厨房大屏配置API
        print("2. 测试厨房大屏配置API...")
        config_req = urllib.request.Request(f"{base_url}/api/kitchen-display-config")
        config_response = opener.open(config_req)

        print(f"   API状态码: {config_response.getcode()}")

        if config_response.getcode() == 200:
            config_data = json.loads(config_response.read().decode('utf-8'))
            print("✅ API调用成功")
            print("📋 配置数据:")
            for key, value in config_data.items():
                print(f"   {key}: {value}")
            return True
        else:
            print(f"❌ API调用失败")
            return False

    except urllib.error.URLError as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("   请确保服务器正在运行在 http://localhost:8001")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_api_routes():
    """测试API路由是否存在"""
    base_url = "http://localhost:8001"

    print("🔍 检查API路由...")

    try:
        # 检查API文档
        docs_req = urllib.request.Request(f"{base_url}/docs")
        docs_response = urllib.request.urlopen(docs_req)
        print(f"API文档状态: {docs_response.getcode()}")

        # 检查OpenAPI规范
        openapi_req = urllib.request.Request(f"{base_url}/openapi.json")
        openapi_response = urllib.request.urlopen(openapi_req)
        if openapi_response.getcode() == 200:
            openapi_data = json.loads(openapi_response.read().decode('utf-8'))
            paths = openapi_data.get("paths", {})

            # 检查厨房大屏配置API是否在路由中
            if "/api/kitchen-display-config" in paths:
                print("✅ 厨房大屏配置API路由已注册")
                return True
            else:
                print("❌ 厨房大屏配置API路由未找到")
                print("📋 已注册的API路由:")
                for path in sorted(paths.keys()):
                    if path.startswith("/api/"):
                        print(f"   {path}")
                return False
        else:
            print("❌ 无法获取OpenAPI规范")
            return False

    except Exception as e:
        print(f"❌ 检查路由失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始API测试...")
    print("=" * 50)
    
    # 设置UTF-8编码
    if sys.platform.startswith('win'):
        import os
        os.system('chcp 65001')
    
    # 测试路由
    route_ok = test_api_routes()
    print()
    
    # 测试API功能
    if route_ok:
        api_ok = test_kitchen_display_config_api()
    else:
        api_ok = False
    
    print()
    print("=" * 50)
    if route_ok and api_ok:
        print("✅ 所有测试通过")
    else:
        print("❌ 测试失败")
        if not route_ok:
            print("   - API路由问题")
        if not api_ok:
            print("   - API功能问题")

if __name__ == "__main__":
    main()
