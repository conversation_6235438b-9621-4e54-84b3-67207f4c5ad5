#!/usr/bin/env python3
"""
测试打荷员菜品完成通知功能
"""

import asyncio
import json
from datetime import datetime
from main import dish_completion_notifications

async def test_notification():
    """测试通知功能"""
    print("🧪 开始测试打荷员菜品完成通知功能...")
    
    # 模拟添加一个通知
    notification = {
        'id': 1,
        'room_number': '1号镜湖包厢',
        'dish_name': '红烧肉',
        'message': '1号镜湖包厢红烧肉，跑菜',
        'timestamp': datetime.now().isoformat(),
        'processed': False
    }
    
    dish_completion_notifications.append(notification)
    
    print(f"✅ 添加测试通知: {notification['message']}")
    print(f"📋 当前通知列表长度: {len(dish_completion_notifications)}")
    
    # 打印通知内容
    for i, notif in enumerate(dish_completion_notifications):
        print(f"  {i+1}. {notif['message']} (时间: {notif['timestamp']})")
    
    print("\n🔍 现在可以在厨房大屏界面检查是否收到通知")
    print("📱 访问: http://localhost:8004/kitchen/display-new")
    print("🎯 应该看到Toast通知: '1号镜湖包厢红烧肉，跑菜'")

if __name__ == "__main__":
    asyncio.run(test_notification())
