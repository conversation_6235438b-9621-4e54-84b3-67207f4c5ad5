#!/usr/bin/env python3
"""
修复枚举值不匹配问题
清理所有订单项数据，重新开始
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from sqlalchemy import text
from core.database import get_db

def fix_enum_issue():
    """修复枚举值不匹配问题"""
    db = next(get_db())
    
    try:
        print("🔄 开始修复枚举值不匹配问题...")
        
        # 删除所有订单项
        result1 = db.execute(text("DELETE FROM order_items"))
        print(f"✅ 删除了 {result1.rowcount} 个订单项")
        
        # 删除所有订单
        result2 = db.execute(text("DELETE FROM orders"))
        print(f"✅ 删除了 {result2.rowcount} 个订单")
        
        # 重置餐桌状态
        result3 = db.execute(text("""
            UPDATE tables 
            SET status = 'available',
                current_guests = 0,
                assigned_waiter_id = NULL
        """))
        print(f"✅ 重置了 {result3.rowcount} 个餐桌状态")
        
        # 清理用户的包厢分配
        result4 = db.execute(text("""
            UPDATE users 
            SET assigned_tables = NULL
        """))
        print(f"✅ 清理了 {result4.rowcount} 个用户的包厢分配")
        
        # 提交更改
        db.commit()
        print("✅ 枚举值不匹配问题修复完成！")
        print("📝 所有订单和订单项已清理，系统已重置为初始状态")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 修复失败: {e}")
        raise
    finally:
        db.close()

def show_status():
    """显示当前状态"""
    db = next(get_db())
    
    try:
        print("\n📊 当前系统状态:")
        
        # 统计订单数量
        result = db.execute(text("SELECT COUNT(*) FROM orders"))
        order_count = result.scalar()
        print(f"  订单数量: {order_count}")
        
        # 统计订单项数量
        result = db.execute(text("SELECT COUNT(*) FROM order_items"))
        item_count = result.scalar()
        print(f"  订单项数量: {item_count}")
        
        # 统计餐桌状态
        result = db.execute(text("""
            SELECT status, COUNT(*) as count 
            FROM tables 
            GROUP BY status
        """))
        
        print("  餐桌状态:")
        for row in result:
            status, count = row
            print(f"    {status}: {count} 个")
        
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🏨 暨阳湖大酒店传菜管理系统 - 枚举值修复工具")
    print("=" * 50)
    
    # 显示修复前状态
    print("修复前状态:")
    show_status()
    
    # 确认修复
    confirm = input("\n⚠️ 确定要清理所有订单数据并重置系统吗？(y/N): ")
    if confirm.lower() in ['y', 'yes']:
        fix_enum_issue()
        
        # 显示修复后状态
        print("\n修复后状态:")
        show_status()
    else:
        print("❌ 操作已取消")
