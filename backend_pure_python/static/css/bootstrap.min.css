/* Bootstrap 5.3.0 简化版本 - 包含系统所需的基本样式 */

/* CSS Reset */
*,*::before,*::after{box-sizing:border-box}
body{margin:0;font-family:system-ui,-apple-system,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue","Noto Sans","Liberation Sans",<PERSON><PERSON>,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-size:1rem;font-weight:400;line-height:1.5;color:#212529;background-color:#fff}

/* Container */
.container,.container-fluid{width:100%;padding-right:var(--bs-gutter-x,.75rem);padding-left:var(--bs-gutter-x,.75rem);margin-right:auto;margin-left:auto}
.container{max-width:1320px}

/* Grid System */
.row{display:flex;flex-wrap:wrap;margin-right:calc(-.5 * var(--bs-gutter-x));margin-left:calc(-.5 * var(--bs-gutter-x))}
.col,.col-1,.col-2,.col-3,.col-4,.col-5,.col-6,.col-7,.col-8,.col-9,.col-10,.col-11,.col-12{flex:0 0 auto;width:100%}
.col-1{width:8.33333333%}.col-2{width:16.66666667%}.col-3{width:25%}.col-4{width:33.33333333%}.col-5{width:41.66666667%}.col-6{width:50%}.col-7{width:58.33333333%}.col-8{width:66.66666667%}.col-9{width:75%}.col-10{width:83.33333333%}.col-11{width:91.66666667%}.col-12{width:100%}

/* Buttons */
.btn{display:inline-block;font-weight:400;line-height:1.5;color:#212529;text-align:center;text-decoration:none;vertical-align:middle;cursor:pointer;user-select:none;background-color:transparent;border:1px solid transparent;padding:.375rem .75rem;font-size:1rem;border-radius:.375rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out}
.btn:hover{color:#212529}
.btn-primary{color:#fff;background-color:#0d6efd;border-color:#0d6efd}
.btn-primary:hover{color:#fff;background-color:#0b5ed7;border-color:#0a58ca}
.btn-secondary{color:#fff;background-color:#6c757d;border-color:#6c757d}
.btn-success{color:#fff;background-color:#198754;border-color:#198754}
.btn-danger{color:#fff;background-color:#dc3545;border-color:#dc3545}
.btn-warning{color:#000;background-color:#ffc107;border-color:#ffc107}
.btn-info{color:#000;background-color:#0dcaf0;border-color:#0dcaf0}
.btn-light{color:#000;background-color:#f8f9fa;border-color:#f8f9fa}
.btn-dark{color:#fff;background-color:#212529;border-color:#212529}
.btn-sm{padding:.25rem .5rem;font-size:.875rem;border-radius:.25rem}
.btn-lg{padding:.5rem 1rem;font-size:1.25rem;border-radius:.5rem}

/* Forms */
.form-control{display:block;width:100%;padding:.375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#212529;background-color:#fff;background-image:none;border:1px solid #ced4da;appearance:none;border-radius:.375rem;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out}
.form-control:focus{color:#212529;background-color:#fff;border-color:#86b7fe;outline:0;box-shadow:0 0 0 .25rem rgba(13,110,253,.25)}
.form-select{display:block;width:100%;padding:.375rem 2.25rem .375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#212529;background-color:#fff;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right .75rem center;background-size:16px 12px;border:1px solid #ced4da;border-radius:.375rem;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;appearance:none}
.form-label{margin-bottom:.5rem}
.form-check{display:block;min-height:1.5rem;padding-left:1.5em;margin-bottom:.125rem}
.form-check-input{width:1em;height:1em;margin-top:.25em;vertical-align:top;background-color:#fff;background-repeat:no-repeat;background-position:center;background-size:contain;border:1px solid rgba(0,0,0,.25);appearance:none;color-adjust:exact}
.form-check-input[type=checkbox]{border-radius:.25em}

/* Cards */
.card{position:relative;display:flex;flex-direction:column;min-width:0;word-wrap:break-word;background-color:#fff;background-clip:border-box;border:1px solid rgba(0,0,0,.125);border-radius:.375rem}
.card-body{flex:1 1 auto;padding:1rem}
.card-title{margin-bottom:.5rem}
.card-header{padding:.5rem 1rem;margin-bottom:0;background-color:rgba(0,0,0,.03);border-bottom:1px solid rgba(0,0,0,.125)}

/* Tables */
.table{width:100%;margin-bottom:1rem;color:#212529;vertical-align:top;border-color:#dee2e6}
.table>:not(caption)>*>*{padding:.5rem .5rem;background-color:var(--bs-table-bg);border-bottom-width:1px}
.table>tbody{vertical-align:inherit}
.table>thead{vertical-align:bottom}
.table-striped>tbody>tr:nth-of-type(odd)>td,.table-striped>tbody>tr:nth-of-type(odd)>th{--bs-table-accent-bg:var(--bs-table-striped-bg);color:var(--bs-table-striped-color)}

/* Alerts */
.alert{position:relative;padding:.75rem 1.25rem;margin-bottom:1rem;border:1px solid transparent;border-radius:.375rem}
.alert-primary{color:#084298;background-color:#cfe2ff;border-color:#b6d4fe}
.alert-success{color:#0f5132;background-color:#d1e7dd;border-color:#badbcc}
.alert-danger{color:#842029;background-color:#f8d7da;border-color:#f5c2c7}
.alert-warning{color:#664d03;background-color:#fff3cd;border-color:#ffecb5}

/* Navbar */
.navbar{position:relative;display:flex;flex-wrap:wrap;align-items:center;justify-content:space-between;padding-top:.5rem;padding-bottom:.5rem}
.navbar-brand{padding-top:.3125rem;padding-bottom:.3125rem;margin-right:1rem;font-size:1.25rem;text-decoration:none;white-space:nowrap}
.navbar-nav{display:flex;flex-direction:column;padding-left:0;margin-bottom:0;list-style:none}
.nav-link{display:block;padding:.5rem 1rem;color:#0d6efd;text-decoration:none;transition:color .15s ease-in-out,background-color .15s ease-in-out}

/* Utilities */
.d-none{display:none!important}
.d-block{display:block!important}
.d-flex{display:flex!important}
.justify-content-center{justify-content:center!important}
.align-items-center{align-items:center!important}
.text-center{text-align:center!important}
.text-end{text-align:right!important}
.text-primary{color:#0d6efd!important}
.text-success{color:#198754!important}
.text-danger{color:#dc3545!important}
.text-warning{color:#ffc107!important}
.bg-primary{background-color:#0d6efd!important}
.bg-light{background-color:#f8f9fa!important}
.bg-dark{background-color:#212529!important}
.m-0{margin:0!important}
.m-1{margin:.25rem!important}
.m-2{margin:.5rem!important}
.m-3{margin:1rem!important}
.m-4{margin:1.5rem!important}
.m-5{margin:3rem!important}
.p-0{padding:0!important}
.p-1{padding:.25rem!important}
.p-2{padding:.5rem!important}
.p-3{padding:1rem!important}
.p-4{padding:1.5rem!important}
.p-5{padding:3rem!important}
.mt-1{margin-top:.25rem!important}
.mt-2{margin-top:.5rem!important}
.mt-3{margin-top:1rem!important}
.mb-1{margin-bottom:.25rem!important}
.mb-2{margin-bottom:.5rem!important}
.mb-3{margin-bottom:1rem!important}
.ms-2{margin-left:.5rem!important}
.me-2{margin-right:.5rem!important}

/* Modal */
.modal{position:fixed;top:0;left:0;z-index:1055;display:none;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;outline:0}
.modal-dialog{position:relative;width:auto;margin:.5rem;pointer-events:none}
.modal-content{position:relative;display:flex;flex-direction:column;width:100%;pointer-events:auto;background-color:#fff;background-clip:padding-box;border:1px solid rgba(0,0,0,.2);border-radius:.5rem;outline:0}
.modal-header{display:flex;flex-shrink:0;align-items:center;justify-content:space-between;padding:1rem 1rem;border-bottom:1px solid #dee2e6;border-top-left-radius:calc(.5rem - 1px);border-top-right-radius:calc(.5rem - 1px)}
.modal-body{position:relative;flex:1 1 auto;padding:1rem}
.modal-footer{display:flex;flex-wrap:wrap;flex-shrink:0;align-items:center;justify-content:flex-end;padding:.75rem;border-top:1px solid #dee2e6;border-bottom-right-radius:calc(.5rem - 1px);border-bottom-left-radius:calc(.5rem - 1px)}

/* Responsive */
@media (min-width:576px){.container{max-width:540px}}
@media (min-width:768px){.container{max-width:720px}.col-md-1{width:8.33333333%}.col-md-2{width:16.66666667%}.col-md-3{width:25%}.col-md-4{width:33.33333333%}.col-md-6{width:50%}.col-md-8{width:66.66666667%}.col-md-12{width:100%}}
@media (min-width:992px){.container{max-width:960px}}
@media (min-width:1200px){.container{max-width:1140px}}
@media (min-width:1400px){.container{max-width:1320px}}
