/* 暨阳湖大酒店传菜管理系统 - 增强样式 */

/* ===== 全局变量和基础设置 ===== */
:root {
    --jiyang-primary: #f2750a;
    --jiyang-secondary: #e35d05;
    --jiyang-success: #198754;
    --jiyang-danger: #dc3545;
    --jiyang-warning: #ffc107;
    --jiyang-info: #0dcaf0;
    --jiyang-light: #f8f9fa;
    --jiyang-dark: #212529;
    
    /* 间距变量 */
    --sidebar-width: 240px;
    --sidebar-width-collapsed: 60px;
    --navbar-height: 60px;
    --border-radius: 8px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* ===== 基础样式重置 ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--jiyang-dark);
    background-color: #f5f6fa;
}

/* ===== 导航栏优化 ===== */
.navbar {
    height: var(--navbar-height);
    box-shadow: var(--box-shadow);
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-bottom: 2px solid var(--jiyang-primary);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.4rem;
    color: var(--jiyang-primary) !important;
    display: flex;
    align-items: center;
    gap: 8px;
}

.navbar-brand i {
    font-size: 1.6rem;
}

/* ===== 侧边栏优化 ===== */
.sidebar {
    width: var(--sidebar-width);
    min-height: calc(100vh - var(--navbar-height));
    background: linear-gradient(180deg, #fff 0%, #f8f9fa 100%);
    border-right: 1px solid #e9ecef;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
    position: fixed;
    top: var(--navbar-height);
    left: 0;
    z-index: 1000;
    overflow-y: auto;
    transition: var(--transition);
}

.sidebar .nav-link {
    color: #495057;
    padding: 12px 20px;
    margin: 4px 12px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    position: relative;
}

.sidebar .nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.sidebar .nav-link:hover {
    background: linear-gradient(135deg, var(--jiyang-primary) 0%, var(--jiyang-secondary) 100%);
    color: white;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(242, 117, 10, 0.3);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--jiyang-primary) 0%, var(--jiyang-secondary) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(242, 117, 10, 0.3);
}

.sidebar .nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: white;
    border-radius: 0 4px 4px 0;
}

.sidebar-heading {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
    margin: 20px 0 8px 0;
    padding: 0 20px;
}

/* ===== 主内容区优化 ===== */
.main-content {
    margin-left: var(--sidebar-width);
    padding: 30px;
    min-height: calc(100vh - var(--navbar-height));
    transition: var(--transition);
}

/* ===== 卡片组件优化 ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background: white;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--jiyang-light) 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    font-weight: 600;
    color: var(--jiyang-dark);
}

.card-body {
    padding: 1.5rem;
}

/* ===== 按钮优化 ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--jiyang-primary) 0%, var(--jiyang-secondary) 100%);
    box-shadow: 0 2px 8px rgba(242, 117, 10, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--jiyang-secondary) 0%, #d64a04 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(242, 117, 10, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--jiyang-success) 0%, #157347 100%);
}

.btn-danger {
    background: linear-gradient(135deg, var(--jiyang-danger) 0%, #b02a37 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--jiyang-warning) 0%, #ffca2c 100%);
    color: var(--jiyang-dark);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.125rem;
}

/* ===== 表格优化 ===== */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--jiyang-light) 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: var(--jiyang-dark);
    padding: 16px;
}

.table tbody td {
    padding: 16px;
    border-top: 1px solid #f1f3f4;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* ===== 表单优化 ===== */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 12px 16px;
    transition: var(--transition);
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--jiyang-primary);
    box-shadow: 0 0 0 0.2rem rgba(242, 117, 10, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--jiyang-dark);
    margin-bottom: 8px;
}

.form-select {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 12px 16px;
}

/* ===== 状态标签优化 ===== */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
}

.table-status-available { 
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: var(--jiyang-success);
    border-left: 4px solid var(--jiyang-success);
}

.table-status-occupied { 
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: var(--jiyang-danger);
    border-left: 4px solid var(--jiyang-danger);
}

.table-status-reserved { 
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 4px solid var(--jiyang-warning);
}

.table-status-cleaning { 
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: var(--jiyang-info);
    border-left: 4px solid var(--jiyang-info);
}

/* ===== 订单状态样式 ===== */
.order-status-draft { color: #6c757d; font-weight: 500; }
.order-status-confirmed { color: #0d6efd; font-weight: 500; }
.order-status-cooking { color: #fd7e14; font-weight: 500; }
.order-status-ready { color: var(--jiyang-success); font-weight: 500; }
.order-status-served { color: #20c997; font-weight: 500; }
.order-status-completed { color: var(--jiyang-success); font-weight: 500; }
.order-status-cancelled { color: var(--jiyang-danger); font-weight: 500; }

/* ===== 菜品分类标签 ===== */
.dish-category {
    background: linear-gradient(135deg, var(--jiyang-primary) 0%, var(--jiyang-secondary) 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-block;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 20px 15px;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* ===== 移动端触摸优化 ===== */
@media (max-width: 768px) {
    .btn {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
        font-size: 1rem;
    }

    .btn-sm {
        min-height: 40px;
        padding: 10px 14px;
        font-size: 0.9rem;
    }

    .btn-lg {
        min-height: 48px;
        padding: 16px 24px;
        font-size: 1.1rem;
    }

    .form-control {
        min-height: 44px;
        font-size: 16px; /* 防止iOS缩放 */
        padding: 14px 16px;
    }

    .form-select {
        min-height: 44px;
        font-size: 16px; /* 防止iOS缩放 */
        padding: 14px 16px;
    }

    input, textarea, select {
        font-size: 16px !important; /* 防止iOS缩放 */
    }

    .sidebar .nav-link {
        padding: 16px 20px;
        font-size: 1rem;
        min-height: 48px;
        display: flex;
        align-items: center;
    }

    .table td, .table th {
        padding: 12px 8px;
        font-size: 0.9rem;
    }

    /* 移动端卡片优化 */
    .card {
        margin-bottom: 1rem;
        border-radius: 12px;
    }

    .card-body {
        padding: 1.25rem;
    }

    /* 移动端表格滚动 */
    .table-responsive {
        border-radius: 12px;
        box-shadow: var(--box-shadow);
    }

    /* 移动端导航栏 */
    .navbar {
        padding: 0.75rem 1rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    /* 移动端侧边栏遮罩 */
    .sidebar.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }

    /* 移动端主内容区 */
    .main-content {
        padding: 15px;
        min-height: calc(100vh - var(--navbar-height));
    }

    /* 移动端统计卡片 */
    .stats-card {
        margin-bottom: 1rem;
        text-align: center;
    }

    .stats-number {
        font-size: 1.75rem;
    }

    /* 移动端模态框优化 */
    .modal-dialog {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }

    /* 移动端下拉菜单 */
    .dropdown-menu {
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
}

/* ===== 平板优化 ===== */
@media (min-width: 769px) and (max-width: 1024px) {
    .sidebar {
        width: 200px;
    }
    
    .main-content {
        margin-left: 200px;
        padding: 25px;
    }
    
    .sidebar .nav-link {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
}

/* ===== 动画效果 ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

.sidebar .nav-link {
    animation: slideIn 0.3s ease-out;
}

/* ===== 加载状态 ===== */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--jiyang-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 工具类 ===== */
.text-jiyang-primary { color: var(--jiyang-primary) !important; }
.bg-jiyang-primary { background-color: var(--jiyang-primary) !important; }
.border-jiyang-primary { border-color: var(--jiyang-primary) !important; }

.shadow-jiyang {
    box-shadow: 0 4px 16px rgba(242, 117, 10, 0.2) !important;
}

.rounded-jiyang {
    border-radius: var(--border-radius) !important;
}

/* ===== 特殊组件 ===== */
.stats-card {
    background: linear-gradient(135deg, white 0%, #f8f9fa 100%);
    border-left: 4px solid var(--jiyang-primary);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--jiyang-primary);
}

.stats-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* ===== 工作台统计卡片 ===== */
.border-left-primary {
    border-left: 4px solid var(--jiyang-primary) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.border-left-danger {
    border-left: 4px solid var(--jiyang-danger) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.border-left-success {
    border-left: 4px solid var(--jiyang-success) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.border-left-warning {
    border-left: 4px solid var(--jiyang-warning) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.text-gray-800 {
    color: var(--jiyang-dark) !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-xs {
    font-size: 0.75rem !important;
}

/* ===== 无障碍优化 ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .btn, .card, .sidebar .nav-link {
        transition: none !important;
    }

    .loading::after {
        animation: none !important;
    }
}

/* ===== 浅色主题优化 ===== */
body {
    background: linear-gradient(135deg, #f5f6fa 0%, #ffffff 100%);
    color: var(--jiyang-dark);
}

.navbar {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 2px solid var(--jiyang-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
    border-right: 1px solid #e9ecef;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    color: var(--jiyang-dark);
}

.table {
    background: #ffffff;
    color: var(--jiyang-dark);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: var(--jiyang-dark);
}

.form-control {
    background: #ffffff;
    border: 2px solid #e9ecef;
    color: var(--jiyang-dark);
}

.form-control:focus {
    background: #ffffff;
    border-color: var(--jiyang-primary);
    color: var(--jiyang-dark);
}

/* ===== 打印样式 ===== */
@media print {
    .sidebar, .navbar, .btn {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* ===== 复选框和单选按钮样式修复 ===== */
.form-check-input {
    width: 1.25em !important;
    height: 1.25em !important;
    margin-top: 0.125em !important;
    vertical-align: top !important;
    background-color: #fff !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: contain !important;
    border: 2px solid #dee2e6 !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    print-color-adjust: exact !important;
    transition: all 0.15s ease-in-out !important;
}

.form-check-input:checked {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
}

.form-check-input[type="checkbox"]:checked {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e") !important;
}

.form-check-input[type="radio"]:checked {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e") !important;
}

.form-check-input[type="radio"] {
    border-radius: 50% !important;
}

.form-check-input:focus {
    border-color: #86b7fe !important;
    outline: 0 !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

.form-check-input:disabled {
    pointer-events: none !important;
    filter: none !important;
    opacity: 0.5 !important;
}

.form-check-label {
    color: #212529 !important;
    cursor: pointer !important;
    font-weight: 500 !important;
}

.form-check {
    display: block !important;
    min-height: 1.5rem !important;
    padding-left: 1.5em !important;
    margin-bottom: 0.125rem !important;
}

.form-check .form-check-input {
    float: left !important;
    margin-left: -1.5em !important;
}

/* ===== 按钮样式美化 ===== */
.btn-refresh {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border: none !important;
    color: white !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2) !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
}

.btn-refresh:hover {
    background: linear-gradient(135deg, #218838, #1ea080) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3) !important;
    color: white !important;
}

.btn-refresh:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2) !important;
}

.btn-logout {
    background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
    border: 1px solid #dc3545 !important;
    color: white !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2) !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    cursor: pointer !important;
    text-decoration: none !important;
    min-height: 32px !important;
}

.btn-logout:hover {
    background: linear-gradient(135deg, #c82333, #dc2626) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3) !important;
    color: white !important;
}

.btn-logout:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2) !important;
}

/* ===== 登录信息显示优化 ===== */
.user-info {
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
    margin-left: auto !important;
    padding: 8px 12px !important;
    background: rgba(248, 249, 250, 0.8) !important;
    border-radius: 8px !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.user-info .user-details {
    display: flex !important;
    align-items: center !important;
    line-height: 1.2 !important;
    padding-right: 16px !important;
    border-right: 1px solid rgba(0, 0, 0, 0.15) !important;
}

.user-info .user-name {
    font-weight: 600 !important;
    color: #2c3e50 !important;
    font-size: 0.95rem !important;
    margin: 0 !important;
    white-space: nowrap !important;
}

.user-info .user-actions {
    display: flex !important;
    align-items: center !important;
}

/* ===== 导航栏布局优化 ===== */
.navbar .container-fluid {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.navbar-nav {
    display: flex !important;
    align-items: center !important;
    margin-left: auto !important;
}
