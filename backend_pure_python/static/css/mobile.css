/* 暨阳湖大酒店传菜管理系统 - 移动端样式 */

/* 移动端基础样式 */
@media (max-width: 768px) {
    /* 全局字体大小调整 */
    body {
        font-size: 16px !important;
        line-height: 1.4;
    }

    /* 标题字体大小 */
    h1, .h1 {
        font-size: 24px !important;
    }

    h2, .h2 {
        font-size: 22px !important;
    }

    h3, .h3 {
        font-size: 20px !important;
    }

    h4, .h4 {
        font-size: 18px !important;
    }

    h5, .h5 {
        font-size: 16px !important;
    }

    h6, .h6 {
        font-size: 14px !important;
    }

    /* 按钮最小尺寸 */
    .btn {
        min-height: 44px !important;
        min-width: 44px !important;
        padding: 12px 16px !important;
        font-size: 16px !important;
        border-radius: 8px !important;
    }

    .btn-sm {
        min-height: 40px !important;
        min-width: 40px !important;
        padding: 10px 14px !important;
        font-size: 14px !important;
    }

    .btn-lg {
        min-height: 50px !important;
        min-width: 50px !important;
        padding: 14px 20px !important;
        font-size: 18px !important;
    }

    /* 表单控件优化 */
    .form-control {
        min-height: 44px !important;
        font-size: 16px !important;
        padding: 12px 16px !important;
    }

    .form-select {
        min-height: 44px !important;
        font-size: 16px !important;
        padding: 12px 16px !important;
    }

    /* 卡片间距优化 */
    .card {
        margin-bottom: 15px !important;
    }

    .card-body {
        padding: 15px !important;
    }

    .card-header {
        padding: 12px 15px !important;
        font-size: 18px !important;
        font-weight: 600;
    }

    /* 容器间距优化 */
    .container-fluid {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    /* 导航栏优化 */
    .navbar {
        padding: 8px 15px !important;
    }

    .navbar-brand {
        font-size: 18px !important;
    }

    .nav-link {
        font-size: 16px !important;
        padding: 10px 15px !important;
    }

    /* 表格优化 */
    .table {
        font-size: 14px !important;
    }

    .table td, .table th {
        padding: 12px 8px !important;
    }

    /* 模态框优化 */
    .modal-dialog {
        margin: 10px !important;
    }

    .modal-content {
        border-radius: 12px !important;
    }

    .modal-header {
        padding: 15px !important;
        font-size: 18px !important;
    }

    .modal-body {
        padding: 15px !important;
        font-size: 16px !important;
    }

    /* 指令内容特别加大 */
    .instruction-content,
    .command-content,
    .waiter-action,
    .notification-item {
        font-size: 18px !important;
        font-weight: 600 !important;
        line-height: 1.3 !important;
        padding: 15px !important;
    }

    /* 菜品名称加大 */
    .dish-name {
        font-size: 16px !important;
        font-weight: 600 !important;
    }

    /* 包厢号加大 */
    .room-number,
    .table-number {
        font-size: 18px !important;
        font-weight: bold !important;
    }

    /* 状态标签优化 */
    .badge {
        font-size: 14px !important;
        padding: 6px 12px !important;
    }

    /* 列表项优化 */
    .list-group-item {
        padding: 15px !important;
        font-size: 16px !important;
    }

    /* 输入组优化 */
    .input-group-text {
        font-size: 16px !important;
        padding: 12px 16px !important;
    }

    /* 分页优化 */
    .page-link {
        font-size: 16px !important;
        padding: 12px 16px !important;
        min-height: 44px !important;
        min-width: 44px !important;
    }

    /* 面包屑导航优化 */
    .breadcrumb-item {
        font-size: 16px !important;
    }

    /* 警告框优化 */
    .alert {
        font-size: 16px !important;
        padding: 15px !important;
    }

    /* 工具提示优化 */
    .tooltip {
        font-size: 14px !important;
    }

    /* 下拉菜单优化 */
    .dropdown-item {
        font-size: 16px !important;
        padding: 12px 20px !important;
    }

    /* 进度条优化 */
    .progress {
        height: 25px !important;
    }

    /* 标签页优化 */
    .nav-tabs .nav-link {
        font-size: 16px !important;
        padding: 12px 16px !important;
    }

    /* 手风琴优化 */
    .accordion-button {
        font-size: 16px !important;
        padding: 15px !important;
    }

    /* 轮播图优化 */
    .carousel-caption {
        font-size: 16px !important;
    }

    /* 紧凑布局 - 减少空白区域 */
    .row {
        margin-left: -5px !important;
        margin-right: -5px !important;
    }

    .col, [class*="col-"] {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }

    /* 移除不必要的间距 */
    .mb-3 {
        margin-bottom: 10px !important;
    }

    .mb-4 {
        margin-bottom: 15px !important;
    }

    .mt-3 {
        margin-top: 10px !important;
    }

    .mt-4 {
        margin-top: 15px !important;
    }

    /* 优化滚动条 */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
}

/* 平板端优化 (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
    body {
        font-size: 15px !important;
    }

    .btn {
        min-height: 42px !important;
        min-width: 42px !important;
        font-size: 15px !important;
    }

    .form-control, .form-select {
        min-height: 42px !important;
        font-size: 15px !important;
    }

    .instruction-content,
    .command-content,
    .waiter-action,
    .notification-item {
        font-size: 17px !important;
    }

    .dish-name {
        font-size: 15px !important;
    }

    .room-number,
    .table-number {
        font-size: 17px !important;
    }
}

/* 触摸友好的交互 */
@media (pointer: coarse) {
    /* 增加点击区域 */
    .clickable,
    .btn,
    .nav-link,
    .dropdown-item,
    .list-group-item-action {
        min-height: 44px !important;
        min-width: 44px !important;
    }

    /* 增加间距避免误触 */
    .btn + .btn {
        margin-left: 8px !important;
    }

    .nav-item + .nav-item {
        margin-left: 4px !important;
    }
}
