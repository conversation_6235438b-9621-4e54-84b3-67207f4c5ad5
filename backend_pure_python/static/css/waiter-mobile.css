/* 服务员移动端专用样式 */

/* ===== 移动端基础设置 ===== */
body.waiter-mobile {
    font-size: 18px;
    line-height: 1.5;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 0;
    margin: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* ===== 移动端导航栏 ===== */
.waiter-navbar {
    background: linear-gradient(135deg, var(--jiyang-primary) 0%, var(--jiyang-secondary) 100%);
    color: white;
    padding: 15px 20px;
    box-shadow: 0 4px 12px rgba(242, 117, 10, 0.3);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.waiter-navbar h1 {
    font-size: 1.4rem;
    margin: 0;
    font-weight: 700;
    text-align: center;
}

.waiter-navbar .logout-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid white;
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 600;
    min-height: 44px;
    min-width: 80px;
}

.waiter-navbar .logout-btn:hover {
    background: white;
    color: var(--jiyang-primary);
}

/* ===== 移动端主容器 ===== */
.waiter-container {
    padding: 20px;
    max-width: 100%;
    margin: 0 auto;
}

/* ===== 移动端卡片 ===== */
.waiter-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    border: none;
}

.waiter-card-header {
    background: linear-gradient(135deg, var(--jiyang-light) 0%, #e9ecef 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #dee2e6;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--jiyang-dark);
}

.waiter-card-body {
    padding: 20px;
}

/* ===== 移动端按钮 ===== */
.waiter-btn {
    min-height: 56px;
    min-width: 56px;
    padding: 16px 24px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 12px;
    border: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;
    width: 100%;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.waiter-btn:active {
    transform: scale(0.98);
}

/* ===== 服务员界面按钮统一样式 ===== */
.action-btn {
    width: 120px !important;
    height: 60px !important;
    padding: 8px 12px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    border: none !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-direction: column !important;
    gap: 4px !important;
    margin: 4px !important;
    text-align: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.action-btn:active {
    transform: scale(0.98) !important;
}

/* ===== 指令按钮双排布局 ===== */
.action-buttons-container {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
    gap: 8px !important;
    margin-bottom: 20px !important;
}

.action-buttons-row {
    display: flex !important;
    width: 100% !important;
    justify-content: space-between !important;
    margin-bottom: 8px !important;
}

.action-buttons-row .action-btn {
    flex: 0 0 calc(50% - 4px) !important;
    max-width: calc(50% - 4px) !important;
}

/* ===== 禁用状态样式 ===== */
.disabled-state {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    background: #6c757d !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
}

.disabled-state:hover {
    background: #6c757d !important;
    border-color: #6c757d !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== 开始用餐按钮特殊样式 ===== */
.start-dining-btn {
    min-height: 60px !important;
    font-size: 1.2rem !important;
    font-weight: 700 !important;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3) !important;
    border: none !important;
    animation: pulse-red 2s infinite !important;
}

.start-dining-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4) !important;
}

@keyframes pulse-red {
    0% { box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3); }
    50% { box-shadow: 0 4px 20px rgba(220, 53, 69, 0.5); }
    100% { box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3); }
}

/* ===== 结束用餐按钮特殊样式 ===== */
.end-dining-btn {
    min-height: 60px !important;
    font-size: 1.2rem !important;
    font-weight: 700 !important;
    background: linear-gradient(135deg, #dc3545 0%, #b02a37 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4) !important;
    border: 2px solid #dc3545 !important;
    position: relative !important;
    overflow: hidden !important;
}

.end-dining-btn::before {
    content: '⚠️' !important;
    position: absolute !important;
    left: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    font-size: 1.3rem !important;
    animation: blink 1.5s infinite !important;
}

.end-dining-btn:hover {
    background: linear-gradient(135deg, #b02a37 0%, #a02834 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(220, 53, 69, 0.5) !important;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* ===== 刷新按钮和菜品进度样式 ===== */
.refresh-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.progress-container {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
}

.dish-progress {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.85rem;
    text-align: center;
    min-width: 120px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.progress-text {
    font-weight: bold;
    color: #ffc107;
    margin-bottom: 2px;
}

.remaining-text {
    font-size: 0.75rem;
    color: #ffffff;
    opacity: 0.9;
}

.refresh-btn {
    width: 80px;
    height: 40px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.refresh-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.refresh-btn i {
    font-size: 0.8rem;
}

.waiter-btn-primary {
    background: linear-gradient(135deg, var(--jiyang-primary) 0%, var(--jiyang-secondary) 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(242, 117, 10, 0.3);
}

.waiter-btn-primary:hover {
    box-shadow: 0 6px 16px rgba(242, 117, 10, 0.4);
    transform: translateY(-2px);
}

.waiter-btn-success {
    background: linear-gradient(135deg, var(--jiyang-success) 0%, #157347 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);
}

.waiter-btn-danger {
    background: linear-gradient(135deg, var(--jiyang-danger) 0%, #b02a37 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.waiter-btn-warning {
    background: linear-gradient(135deg, var(--jiyang-warning) 0%, #ffca2c 100%);
    color: var(--jiyang-dark);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.waiter-btn-outline {
    background: white;
    border: 2px solid var(--jiyang-primary);
    color: var(--jiyang-primary);
}

.waiter-btn-outline:hover {
    background: var(--jiyang-primary);
    color: white;
}

/* ===== 移动端表单 ===== */
.waiter-form-control {
    min-height: 56px;
    padding: 16px 20px;
    font-size: 18px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 16px;
}

.waiter-form-control:focus {
    border-color: var(--jiyang-primary);
    box-shadow: 0 0 0 0.2rem rgba(242, 117, 10, 0.25);
    outline: none;
}

.waiter-form-label {
    font-weight: 700;
    font-size: 1rem;
    color: var(--jiyang-dark);
    margin-bottom: 8px;
    display: block;
}

/* ===== 移动端列表 ===== */
.waiter-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.waiter-list-item {
    background: white;
    border-radius: 12px;
    margin-bottom: 12px;
    padding: 16px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--jiyang-primary);
    transition: all 0.3s ease;
}

.waiter-list-item:active {
    transform: scale(0.98);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.waiter-list-item-title {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--jiyang-dark);
    margin-bottom: 8px;
}

.waiter-list-item-subtitle {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 4px;
}

.waiter-list-item-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
}

/* ===== 移动端状态标签 ===== */
.waiter-status {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    min-width: 80px;
}

.waiter-status-pending {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.waiter-status-ready {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: var(--jiyang-success);
    border: 1px solid #c3e6cb;
}

.waiter-status-served {
    background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
    color: #6c757d;
    border: 1px solid #d6d8db;
}

/* ===== 移动端网格布局 ===== */
.waiter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.waiter-grid-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.waiter-grid-item:active {
    transform: scale(0.95);
    border-color: var(--jiyang-primary);
}

.waiter-grid-item-icon {
    font-size: 2rem;
    color: var(--jiyang-primary);
    margin-bottom: 8px;
}

.waiter-grid-item-title {
    font-weight: 700;
    font-size: 1rem;
    color: var(--jiyang-dark);
    margin-bottom: 4px;
}

.waiter-grid-item-subtitle {
    font-size: 0.85rem;
    color: #6c757d;
}

/* ===== 移动端模态框 ===== */
.waiter-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.waiter-modal-content {
    background: white;
    border-radius: 16px;
    padding: 24px;
    width: 100%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.waiter-modal-header {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--jiyang-dark);
    margin-bottom: 16px;
    text-align: center;
}

.waiter-modal-body {
    margin-bottom: 20px;
}

.waiter-modal-footer {
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* ===== 移动端提示消息 ===== */
.waiter-alert {
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 16px;
    font-weight: 600;
    border: none;
}

.waiter-alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: var(--jiyang-success);
}

.waiter-alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: var(--jiyang-danger);
}

.waiter-alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.waiter-alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* ===== 移动端加载状态 ===== */
.waiter-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    font-size: 1.1rem;
    color: var(--jiyang-primary);
}

.waiter-loading::before {
    content: '';
    width: 24px;
    height: 24px;
    border: 3px solid var(--jiyang-primary);
    border-top: 3px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 12px;
}

/* ===== 移动端底部安全区域 ===== */
.waiter-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
}

/* ===== 移动端触摸反馈 ===== */
.waiter-touchable {
    -webkit-tap-highlight-color: rgba(242, 117, 10, 0.2);
    tap-highlight-color: rgba(242, 117, 10, 0.2);
}

/* ===== 移动端横屏适配 ===== */
@media screen and (orientation: landscape) and (max-height: 500px) {
    .waiter-navbar {
        padding: 10px 20px;
    }
    
    .waiter-navbar h1 {
        font-size: 1.2rem;
    }
    
    .waiter-container {
        padding: 15px;
    }
    
    .waiter-btn {
        min-height: 48px;
        padding: 12px 20px;
        font-size: 1rem;
    }
}

/* ===== 移动端无障碍优化 ===== */
@media (prefers-reduced-motion: reduce) {
    .waiter-btn,
    .waiter-list-item,
    .waiter-grid-item {
        transition: none;
    }
    
    .waiter-loading::before {
        animation: none;
    }
}

/* ===== 移动端浅色主题优化 ===== */
body.waiter-mobile {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    color: var(--jiyang-dark);
}

.waiter-card {
    background: #ffffff;
    color: var(--jiyang-dark);
    border: 1px solid #e9ecef;
}

.waiter-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: var(--jiyang-dark);
    border-bottom: 1px solid #dee2e6;
}

.waiter-form-control {
    background: #ffffff;
    border: 2px solid #e9ecef;
    color: var(--jiyang-dark);
}

.waiter-list-item {
    background: #ffffff;
    color: var(--jiyang-dark);
    border: 1px solid #e9ecef;
}

.waiter-grid-item {
    background: #ffffff;
    color: var(--jiyang-dark);
    border: 2px solid #e9ecef;
}

.waiter-modal-content {
    background: #ffffff;
    color: var(--jiyang-dark);
}
