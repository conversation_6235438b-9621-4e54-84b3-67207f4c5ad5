{% extends "base.html" %}

{% block title %}包厢管理 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-door-open"></i>
        包厢管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-refresh btn-sm" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i>
                刷新
            </button>
            {% if user.has_permission('table.manage') %}
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addTableModal">
                <i class="bi bi-plus"></i>
                新增包厢
            </button>
            {% endif %}
        </div>
    </div>
</div>

<!-- 包厢状态统计 -->
<div class="row mb-4">
    {% set available_count = tables|selectattr("status.value", "equalto", "available")|list|length %}
    {% set occupied_count = tables|selectattr("status.value", "equalto", "occupied")|list|length %}
    {% set reserved_count = tables|selectattr("status.value", "equalto", "reserved")|list|length %}
    {% set cleaning_count = tables|selectattr("status.value", "equalto", "cleaning")|list|length %}
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h5">空闲可用</div>
                        <div class="h3">{{ available_count }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h5">使用中</div>
                        <div class="h3">{{ occupied_count }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h5">已预订</div>
                        <div class="h3">{{ reserved_count }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h5">清理中</div>
                        <div class="h3">{{ cleaning_count }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-clockwise" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 包厢列表 -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0">包厢列表</h5>
    </div>
    <div class="card-body">
        {% if tables %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>包厢号</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>容量</th>
                        <th>位置</th>
                        <th>设施</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for table in tables %}
                    <tr>
                        <td>
                            <strong>{{ table.number }}</strong>
                            {% if table.table_type.value == 'vip_room' %}
                            <span class="badge bg-warning">VIP</span>
                            {% endif %}
                        </td>
                        <td>{{ table.name or '-' }}</td>
                        <td>
                            {% if table.table_type.value == 'hall_table' %}大厅包厢
                            {% elif table.table_type.value == 'private_room' %}包厢
                            {% elif table.table_type.value == 'vip_room' %}VIP包厢
                            {% elif table.table_type.value == 'outdoor' %}户外包厢
                            {% else %}{{ table.table_type.value }}
                            {% endif %}
                        </td>
                        <td>{{ table.capacity }}人</td>
                        <td>
                            {% if table.location_description %}
                                {{ table.location_description }}
                            {% else %}
                                {{ (table.floor or '') + ' ' + (table.area or '') }}
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex flex-wrap gap-1">
                                {% if table.has_tv %}<span class="badge bg-secondary">电视</span>{% endif %}
                                {% if table.has_karaoke %}<span class="badge bg-secondary">KTV</span>{% endif %}
                                {% if table.has_mahjong %}<span class="badge bg-secondary">麻将</span>{% endif %}
                                {% if table.has_projector %}<span class="badge bg-secondary">投影</span>{% endif %}
                                {% if table.has_wifi %}<span class="badge bg-secondary">WiFi</span>{% endif %}
                                {% if table.has_air_conditioning %}<span class="badge bg-secondary">空调</span>{% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% if user.has_permission('table.manage') %}
                                <button type="button" class="btn btn-warning btn-sm" title="编辑包厢信息"
                                        data-bs-toggle="modal" data-bs-target="#editModal{{ table.id }}">
                                    <i class="bi bi-pencil"></i> 编辑
                                </button>
                                {% endif %}
                                {% if user.role.value == 'admin' %}
                                <button type="button" class="btn btn-danger btn-sm" title="删除包厢"
                                        onclick="deleteTable({{ table.id }}, '{{ table.number }}')">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-door-open text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">暂无包厢</h4>
            <p class="text-muted">点击上方"新增包厢"按钮添加第一个包厢</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 编辑包厢信息模态框 -->
{% for table in tables %}
<div class="modal fade" id="editModal{{ table.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑包厢信息 - {{ table.number }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="/tables/{{ table.id }}/update">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_number{{ table.id }}" class="form-label">包厢号 *</label>
                                <input type="text" class="form-control" id="edit_number{{ table.id }}"
                                       name="number" value="{{ table.number }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_name{{ table.id }}" class="form-label">名称</label>
                                <input type="text" class="form-control" id="edit_name{{ table.id }}"
                                       name="name" value="{{ table.name or '' }}">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_table_type{{ table.id }}" class="form-label">类型 *</label>
                                <select class="form-select" id="edit_table_type{{ table.id }}" name="table_type" required>
                                    <option value="hall_table" {% if table.table_type.value == 'hall_table' %}selected{% endif %}>大厅包厢</option>
                                    <option value="private_room" {% if table.table_type.value == 'private_room' %}selected{% endif %}>包厢</option>
                                    <option value="vip_room" {% if table.table_type.value == 'vip_room' %}selected{% endif %}>VIP包厢</option>
                                    <option value="outdoor" {% if table.table_type.value == 'outdoor' %}selected{% endif %}>户外包厢</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_capacity{{ table.id }}" class="form-label">容量 *</label>
                                <input type="number" class="form-control" id="edit_capacity{{ table.id }}"
                                       name="capacity" value="{{ table.capacity }}" min="1" max="50" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_location{{ table.id }}" class="form-label">位置</label>
                        <input type="text" class="form-control" id="edit_location{{ table.id }}"
                               name="location" value="{{ table.location_description or '' }}">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">设施</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_has_tv{{ table.id }}"
                                           name="has_tv" {% if table.has_tv %}checked{% endif %}>
                                    <label class="form-check-label" for="edit_has_tv{{ table.id }}">电视</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_has_karaoke{{ table.id }}"
                                           name="has_karaoke" {% if table.has_karaoke %}checked{% endif %}>
                                    <label class="form-check-label" for="edit_has_karaoke{{ table.id }}">KTV</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_has_mahjong{{ table.id }}"
                                           name="has_mahjong" {% if table.has_mahjong %}checked{% endif %}>
                                    <label class="form-check-label" for="edit_has_mahjong{{ table.id }}">麻将</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_has_projector{{ table.id }}"
                                           name="has_projector" {% if table.has_projector %}checked{% endif %}>
                                    <label class="form-check-label" for="edit_has_projector{{ table.id }}">投影</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_has_wifi{{ table.id }}"
                                           name="has_wifi" {% if table.has_wifi %}checked{% endif %}>
                                    <label class="form-check-label" for="edit_has_wifi{{ table.id }}">WiFi</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_has_air_conditioning{{ table.id }}"
                                           name="has_air_conditioning" {% if table.has_air_conditioning %}checked{% endif %}>
                                    <label class="form-check-label" for="edit_has_air_conditioning{{ table.id }}">空调</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存修改</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}

<!-- 新增包厢模态框 -->
<div class="modal fade" id="addTableModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新增包厢</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addTableForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="number" class="form-label">包厢号 *</label>
                                <input type="text" class="form-control" id="number" name="number" required
                                       placeholder="例如: 1号镜湖厅, 2号望湖厅">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">名称</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       placeholder="例如: 镜湖厅">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="table_type" class="form-label">类型 *</label>
                                <select class="form-select" id="table_type" name="table_type" required>
                                    <option value="hall_table">大厅包厢</option>
                                    <option value="private_room">包厢</option>
                                    <option value="vip_room">VIP包厢</option>
                                    <option value="outdoor">户外包厢</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="capacity" class="form-label">容量 *</label>
                                <input type="number" class="form-control" id="capacity" name="capacity"
                                       value="4" min="1" max="50" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">位置</label>
                                <input type="text" class="form-control" id="location" name="location"
                                       placeholder="例如: 二楼东区">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="minimum_charge" class="form-label">最低消费</label>
                                <input type="number" class="form-control" id="minimum_charge" name="minimum_charge"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control" id="description" name="description" rows="2"
                                  placeholder="包厢描述信息"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">设施</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_tv" name="has_tv">
                                    <label class="form-check-label" for="has_tv">电视</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_karaoke" name="has_karaoke">
                                    <label class="form-check-label" for="has_karaoke">KTV</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_mahjong" name="has_mahjong">
                                    <label class="form-check-label" for="has_mahjong">麻将</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_projector" name="has_projector">
                                    <label class="form-check-label" for="has_projector">投影</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_wifi" name="has_wifi" checked>
                                    <label class="form-check-label" for="has_wifi">WiFi</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_air_conditioning" name="has_air_conditioning" checked>
                                    <label class="form-check-label" for="has_air_conditioning">空调</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary" id="createTableBtn">
                        <i class="bi bi-plus-circle"></i> 创建包厢
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // 处理新建包厢表单提交
    document.getElementById('addTableForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const createBtn = document.getElementById('createTableBtn');
        const originalText = createBtn.innerHTML;

        // 显示加载状态
        createBtn.disabled = true;
        createBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 创建中...';

        const formData = new FormData(this);

        fetch('/tables/create', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message || '包厢创建成功');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('addTableModal'));
                if (modal) {
                    modal.hide();
                }

                // 重置表单
                this.reset();

                // 延迟刷新页面数据
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showError(data.message || '创建包厢失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('创建包厢失败，请重试');
        })
        .finally(() => {
            // 恢复按钮状态
            createBtn.disabled = false;
            createBtn.innerHTML = originalText;
        });
    });

    function deleteTable(tableId, tableNumber) {
        if (confirm('确定要删除包厢 ' + tableNumber + ' 吗？')) {
            fetch('/tables/' + tableId + '/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message || '包厢删除成功');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showError(data.message || '删除失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('删除失败，请重试');
            });
        }
    }

    // 授权服务员功能已迁移到订单管理界面

    // doAuthorize函数已迁移到订单管理界面

    // 更换服务员功能已迁移到订单管理界面

    // doChangeWaiter函数已迁移到订单管理界面

    // 强制结束包厢功能已迁移到订单管理界面
    
    // 自动刷新包厢状态
    setInterval(function() {
        location.reload();
    }, 60000); // 每分钟刷新一次
</script>
{% endblock %}
