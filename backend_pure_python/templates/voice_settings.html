{% extends "base.html" %}

{% block title %}语音播报设置{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-volume-up"></i> 语音播报设置</h2>
                <a href="/dashboard" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear"></i> 语音播报配置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="voiceConfigForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-check-circle-fill text-success"></i> 厨房制作完成播放
                                    </label>
                                    <div class="alert alert-info mb-0">
                                        <i class="bi bi-info-circle"></i>
                                        <strong>系统必需功能：</strong>厨房制作完成播放为系统必需功能，已默认启用
                                    </div>
                                    <input type="hidden" id="voiceEnabled" value="true">
                                </div>

                                <div class="mb-3">
                                    <label for="repeatCount" class="form-label">
                                        <i class="bi bi-repeat"></i> 播报次数
                                    </label>
                                    <select class="form-select" id="repeatCount">
                                        <option value="1">1次</option>
                                        <option value="2" selected>2次</option>
                                        <option value="3">3次</option>
                                        <option value="4">4次</option>
                                        <option value="5">5次</option>
                                    </select>
                                    <div class="form-text">设置语音播报重复次数</div>
                                </div>

                                <div class="mb-3">
                                    <label for="repeatInterval" class="form-label">
                                        <i class="bi bi-clock"></i> 播报间隔
                                    </label>
                                    <select class="form-select" id="repeatInterval">
                                        <option value="1">1秒</option>
                                        <option value="2">2秒</option>
                                        <option value="3" selected>3秒</option>
                                        <option value="4">4秒</option>
                                        <option value="5">5秒</option>
                                        <option value="8">8秒</option>
                                        <option value="10">10秒</option>
                                    </select>
                                    <div class="form-text">设置两次播报之间的间隔时间</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="voiceRate" class="form-label">
                                        <i class="bi bi-speedometer"></i> 语音语速
                                    </label>
                                    <input type="range" class="form-range" id="voiceRate" min="0.5" max="2.0" step="0.1" value="0.8">
                                    <div class="d-flex justify-content-between">
                                        <small>慢</small>
                                        <small id="rateValue">0.8</small>
                                        <small>快</small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="voiceVolume" class="form-label">
                                        <i class="bi bi-volume-up"></i> 语音音量
                                    </label>
                                    <input type="range" class="form-range" id="voiceVolume" min="0.0" max="1.0" step="0.1" value="1.0">
                                    <div class="d-flex justify-content-between">
                                        <small>小</small>
                                        <small id="volumeValue">1.0</small>
                                        <small>大</small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="voicePitch" class="form-label">
                                        <i class="bi bi-music-note"></i> 语音音调
                                    </label>
                                    <input type="range" class="form-range" id="voicePitch" min="0.5" max="2.0" step="0.1" value="1.0">
                                    <div class="d-flex justify-content-between">
                                        <small>低</small>
                                        <small id="pitchValue">1.0</small>
                                        <small>高</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-play-circle"></i> 语音测试
                                    </label>
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-info" onclick="testVoice()">
                                            <i class="bi bi-play"></i> 测试语音播报
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="stopVoice()">
                                            <i class="bi bi-stop"></i> 停止播报
                                        </button>
                                    </div>
                                    <div class="form-text">点击测试按钮预览当前设置的语音效果</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check"></i> 保存设置
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetToDefault()">
                                <i class="bi bi-arrow-clockwise"></i> 恢复默认
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> 使用说明
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li><strong>启用语音播报</strong>：控制是否在厨房制作完成时播放语音提示</li>
                        <li><strong>播报次数</strong>：设置语音播报重复的次数（1-5次）</li>
                        <li><strong>播报间隔</strong>：设置两次播报之间的间隔时间（1-10秒）</li>
                        <li><strong>语音语速</strong>：调整语音播报的速度（0.5-2.0倍速）</li>
                        <li><strong>语音音量</strong>：调整语音播报的音量大小（0.0-1.0）</li>
                        <li><strong>语音音调</strong>：调整语音播报的音调高低（0.5-2.0）</li>
                        <li><strong>语音格式</strong>：播报内容为"XX包厢XX菜品，跑菜"</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载时获取当前配置
    document.addEventListener('DOMContentLoaded', function() {
        loadVoiceConfig();
        
        // 绑定滑块值显示
        document.getElementById('voiceRate').addEventListener('input', function() {
            document.getElementById('rateValue').textContent = this.value;
        });
        
        document.getElementById('voiceVolume').addEventListener('input', function() {
            document.getElementById('volumeValue').textContent = this.value;
        });
        
        document.getElementById('voicePitch').addEventListener('input', function() {
            document.getElementById('pitchValue').textContent = this.value;
        });
    });

    // 加载语音配置
    function loadVoiceConfig() {
        fetch('/api/voice-config')
            .then(response => response.json())
            .then(config => {
                document.getElementById('voiceEnabled').checked = config.voice_enabled;
                document.getElementById('repeatCount').value = config.voice_repeat_count;
                document.getElementById('repeatInterval').value = config.voice_repeat_interval;
                document.getElementById('voiceRate').value = config.voice_rate;
                document.getElementById('voiceVolume').value = config.voice_volume;
                document.getElementById('voicePitch').value = config.voice_pitch;
                
                // 更新显示值
                document.getElementById('rateValue').textContent = config.voice_rate;
                document.getElementById('volumeValue').textContent = config.voice_volume;
                document.getElementById('pitchValue').textContent = config.voice_pitch;
            })
            .catch(error => {
                console.error('加载配置失败:', error);
                alert('加载配置失败');
            });
    }

    // 保存配置
    document.getElementById('voiceConfigForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const config = {
            voice_enabled: true, // 强制启用，系统必需功能
            voice_repeat_count: parseInt(document.getElementById('repeatCount').value),
            voice_repeat_interval: parseInt(document.getElementById('repeatInterval').value),
            voice_rate: parseFloat(document.getElementById('voiceRate').value),
            voice_volume: parseFloat(document.getElementById('voiceVolume').value),
            voice_pitch: parseFloat(document.getElementById('voicePitch').value)
        };
        
        fetch('/api/voice-config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('设置保存成功！配置将立即应用到厨房操作和厨房大屏。');
                // 通知其他页面重新加载语音设置
                broadcastConfigUpdate();
            } else {
                alert('保存失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            alert('保存失败');
        });
    });

    // 测试语音
    function testVoice() {
        const config = {
            enabled: document.getElementById('voiceEnabled').checked,
            repeat_count: parseInt(document.getElementById('repeatCount').value),
            repeat_interval: parseInt(document.getElementById('repeatInterval').value),
            rate: parseFloat(document.getElementById('voiceRate').value),
            volume: parseFloat(document.getElementById('voiceVolume').value),
            pitch: parseFloat(document.getElementById('voicePitch').value)
        };
        
        const testMessage = "1号镜湖厅红烧肉，跑菜";
        playConfigurableVoice(testMessage, config);
    }

    // 停止语音
    function stopVoice() {
        if ('speechSynthesis' in window) {
            speechSynthesis.cancel();
        }
    }

    // 恢复默认设置
    function resetToDefault() {
        if (confirm('确认恢复默认设置？')) {
            document.getElementById('voiceEnabled').checked = true;
            document.getElementById('repeatCount').value = 2;
            document.getElementById('repeatInterval').value = 3;
            document.getElementById('voiceRate').value = 0.8;
            document.getElementById('voiceVolume').value = 1.0;
            document.getElementById('voicePitch').value = 1.0;
            
            // 更新显示值
            document.getElementById('rateValue').textContent = '0.8';
            document.getElementById('volumeValue').textContent = '1.0';
            document.getElementById('pitchValue').textContent = '1.0';
        }
    }

    // 可配置的语音播报函数（与厨房页面相同）
    function playConfigurableVoice(message, config) {
        if (!config.enabled) {
            alert('语音播报已禁用');
            return;
        }

        if (!('speechSynthesis' in window)) {
            alert('浏览器不支持语音播报');
            return;
        }

        let currentRepeat = 0;
        const maxRepeats = config.repeat_count || 2;
        const interval = (config.repeat_interval || 3) * 1000;

        function speakOnce() {
            const utterance = new SpeechSynthesisUtterance(message);
            utterance.lang = 'zh-CN';
            utterance.rate = config.rate || 0.8;
            utterance.volume = config.volume || 1.0;
            utterance.pitch = config.pitch || 1.0;
            
            utterance.onend = function() {
                currentRepeat++;
                if (currentRepeat < maxRepeats) {
                    setTimeout(speakOnce, interval);
                }
            };
            
            utterance.onerror = function(event) {
                console.error('语音播报错误:', event.error);
            };
            
            speechSynthesis.speak(utterance);
            console.log(`🔊 语音播报测试 (${currentRepeat + 1}/${maxRepeats}): ${message}`);
        }

        speakOnce();
    }

    // 广播配置更新
    function broadcastConfigUpdate() {
        // 通过API通知其他页面重新加载语音设置
        fetch('/api/broadcast-voice-config-update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('配置更新已广播到所有页面');
            } else {
                console.error('广播配置更新失败:', data.message);
            }
        })
        .catch(error => {
            console.error('广播配置更新请求失败:', error);
        });
    }
</script>
{% endblock %}
