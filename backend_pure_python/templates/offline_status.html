{% extends "base.html" %}

{% block title %}离线运行状态 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-wifi-off"></i>
        离线运行状态检查
    </h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-check-circle-fill text-success"></i>
                    系统本地化状态
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="bi bi-shield-check"></i> 完全本地化部署</h6>
                    <p class="mb-0">暨阳湖大酒店传菜管理系统已实现完全本地化部署，可在无网络环境下正常运行。</p>
                </div>

                <h6>本地化资源清单：</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>资源类型</th>
                                <th>状态</th>
                                <th>位置</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><i class="bi bi-filetype-css"></i> Bootstrap CSS</td>
                                <td><span class="badge bg-success">本地</span></td>
                                <td>/static/css/bootstrap.min.css</td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-filetype-js"></i> Bootstrap JS</td>
                                <td><span class="badge bg-success">本地</span></td>
                                <td>/static/js/bootstrap.bundle.min.js</td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-fonts"></i> Bootstrap Icons</td>
                                <td><span class="badge bg-success">本地</span></td>
                                <td>/static/css/bootstrap-icons.css</td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-file-font"></i> 图标字体文件</td>
                                <td><span class="badge bg-success">本地</span></td>
                                <td>/static/fonts/bootstrap-icons.*</td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-palette"></i> 自定义样式</td>
                                <td><span class="badge bg-success">本地</span></td>
                                <td>/static/css/jiyang-enhanced.css</td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-volume-up"></i> 语音播报</td>
                                <td><span class="badge bg-success">浏览器内置</span></td>
                                <td>Web Speech API</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    离线功能测试
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">当前网络状态：</label>
                    <div>
                        <span id="current-status" class="badge bg-success">
                            <i class="bi bi-wifi"></i> 在线
                        </span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">语音播报测试：</label>
                    <div>
                        <button type="button" class="btn btn-primary btn-sm" onclick="testVoice()">
                            <i class="bi bi-volume-up"></i> 测试语音
                        </button>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">图标显示测试：</label>
                    <div class="d-flex flex-wrap gap-2">
                        <i class="bi bi-display fs-4 text-primary" title="厨房大屏"></i>
                        <i class="bi bi-megaphone fs-4 text-success" title="指令管理"></i>
                        <i class="bi bi-plus-circle fs-4 text-info" title="新建订单"></i>
                        <i class="bi bi-house fs-4 text-warning" title="工作台"></i>
                        <i class="bi bi-door-open fs-4 text-danger" title="包厢管理"></i>
                    </div>
                </div>

                <div class="alert alert-info">
                    <small>
                        <i class="bi bi-lightbulb"></i>
                        <strong>提示：</strong>即使在完全断网的情况下，系统的所有核心功能都能正常使用。
                    </small>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-gear"></i>
                    离线优化建议
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check text-success"></i>
                        定期备份数据库文件
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success"></i>
                        确保服务器时间准确
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check text-success"></i>
                        监控磁盘空间使用
                    </li>
                    <li class="mb-0">
                        <i class="bi bi-check text-success"></i>
                        定期清理日志文件
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 更新网络状态显示
    function updateNetworkStatus() {
        const isOnline = navigator.onLine;
        const statusElement = document.getElementById('current-status');
        
        if (statusElement) {
            if (isOnline) {
                statusElement.innerHTML = '<i class="bi bi-wifi"></i> 在线';
                statusElement.className = 'badge bg-success';
            } else {
                statusElement.innerHTML = '<i class="bi bi-wifi-off"></i> 离线';
                statusElement.className = 'badge bg-warning';
            }
        }
    }

    // 测试语音播报
    function testVoice() {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance('暨阳湖大酒店传菜管理系统语音播报测试成功');
            utterance.lang = 'zh-CN';
            utterance.rate = 0.8;
            utterance.volume = 1.0;
            speechSynthesis.speak(utterance);
        } else {
            alert('您的浏览器不支持语音播报功能');
        }
    }

    // 监听网络状态变化
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    // 页面加载时更新状态
    document.addEventListener('DOMContentLoaded', updateNetworkStatus);
</script>
{% endblock %}
