{% extends "base.html" %}

{% block title %}服务员指令操作 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-chat-dots"></i>
        服务员指令操作
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <span class="badge bg-primary">{{ user.full_name }}</span>
            {% if user.assigned_tables %}
            <span class="badge bg-success">负责包厢: {{ user.assigned_tables }}</span>
            {% else %}
            <span class="badge bg-warning">未分配包厢</span>
            {% endif %}
        </div>
    </div>
</div>

<!-- 快速指令按钮 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">快速指令</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4 col-lg-3">
                        <button type="button" class="btn btn-success btn-lg w-100" onclick="sendCommand('serve')">
                            <i class="bi bi-arrow-up-circle"></i><br>
                            <span>上菜</span>
                        </button>
                    </div>
                    
                    <div class="col-md-4 col-lg-3">
                        <button type="button" class="btn btn-warning btn-lg w-100" onclick="sendCommand('rush')">
                            <i class="bi bi-clock"></i><br>
                            <span>催菜</span>
                        </button>
                    </div>
                    
                    <div class="col-md-4 col-lg-3">
                        <button type="button" class="btn btn-info btn-lg w-100" onclick="sendCommand('change')">
                            <i class="bi bi-arrow-repeat"></i><br>
                            <span>换菜</span>
                        </button>
                    </div>
                    
                    <div class="col-md-4 col-lg-3">
                        <button type="button" class="btn btn-primary btn-lg w-100" onclick="sendCommand('add_drink')">
                            <i class="bi bi-cup"></i><br>
                            <span>加酒水</span>
                        </button>
                    </div>
                    
                    <div class="col-md-4 col-lg-3">
                        <button type="button" class="btn btn-secondary btn-lg w-100" onclick="sendCommand('add_staple')">
                            <i class="bi bi-bowl"></i><br>
                            <span>上主食</span>
                        </button>
                    </div>
                    
                    <div class="col-md-4 col-lg-3">
                        <button type="button" class="btn btn-warning btn-lg w-100" onclick="sendCommand('add_fruit')">
                            <i class="bi bi-apple"></i><br>
                            <span>上水果</span>
                        </button>
                    </div>
                    
                    <div class="col-md-4 col-lg-3">
                        <button type="button" class="btn btn-danger btn-lg w-100" onclick="sendCommand('special')">
                            <i class="bi bi-star"></i><br>
                            <span>特殊服务</span>
                        </button>
                    </div>
                    
                    <div class="col-md-4 col-lg-3">
                        <button type="button" class="btn btn-outline-primary btn-lg w-100" onclick="showCustomCommand()">
                            <i class="bi bi-pencil"></i><br>
                            <span>自定义指令</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 我的指令记录 -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0">我的指令记录</h5>
    </div>
    <div class="card-body">
        {% if my_commands %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>时间</th>
                        <th>包厢</th>
                        <th>指令类型</th>
                        <th>指令内容</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for command in my_commands %}
                    <tr>
                        <td>{{ command.created_at.strftime('%H:%M:%S') }}</td>
                        <td>
                            <span class="badge bg-primary">{{ command.room_number }}</span>
                        </td>
                        <td>
                            <span class="badge bg-{% if command.action_type == 'serve' %}success{% elif command.action_type == 'rush' %}warning{% elif command.action_type == 'change' %}info{% elif command.action_type == 'add_drink' %}primary{% elif command.action_type == 'special' %}danger{% else %}secondary{% endif %}">
                                {% if command.action_type == 'serve' %}上菜
                                {% elif command.action_type == 'rush' %}催菜
                                {% elif command.action_type == 'change' %}换菜
                                {% elif command.action_type == 'add_drink' %}加酒水
                                {% elif command.action_type == 'add_staple' %}上主食
                                {% elif command.action_type == 'add_fruit' %}上水果
                                {% elif command.action_type == 'special' %}特殊服务
                                {% else %}{{ command.action_type }}
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            {% if command.action_content %}
                            <small>{{ command.action_content }}</small>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if command.is_processed %}
                            <span class="badge bg-success">已处理</span>
                            {% else %}
                            <span class="badge bg-warning">待处理</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-chat-dots text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">暂无指令记录</h4>
            <p class="text-muted">点击上方按钮发送指令</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 自定义指令模态框 -->
<div class="modal fade" id="customCommandModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">发送自定义指令</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="roomNumber" class="form-label">包厢号 *</label>
                    <select class="form-select" id="roomNumber" required>
                        <option value="">请选择包厢</option>
                        {% if user.assigned_tables %}
                            {% for room in user.assigned_tables.split(',') %}
                            <option value="{{ room.strip() }}">{{ room.strip() }}</option>
                            {% endfor %}
                        {% endif %}
                    </select>
                </div>
                <div class="mb-3">
                    <label for="commandType" class="form-label">指令类型 *</label>
                    <select class="form-select" id="commandType" required>
                        <option value="serve">上菜</option>
                        <option value="rush">催菜</option>
                        <option value="change">换菜</option>
                        <option value="add_drink">加酒水</option>
                        <option value="add_staple">上主食</option>
                        <option value="add_fruit">上水果</option>
                        <option value="special">特殊服务</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="commandContent" class="form-label">指令内容</label>
                    <textarea class="form-control" id="commandContent" rows="3" 
                              placeholder="请输入具体的指令内容..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="sendCustomCommand()">发送指令</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    function sendCommand(type) {
        // 检查是否有分配的包厢
        {% if not user.assigned_tables %}
        alert('您还没有被分配包厢，请联系餐饮经理');
        return;
        {% endif %}
        
        // 如果只有一个包厢，直接发送
        const rooms = '{{ user.assigned_tables }}'.split(',');
        if (rooms.length === 1) {
            sendCommandToRoom(rooms[0].trim(), type, '');
        } else {
            // 多个包厢，显示选择
            showRoomSelection(type);
        }
    }
    
    function showRoomSelection(type) {
        const rooms = '{{ user.assigned_tables }}'.split(',');
        let html = '<div class="mb-3"><label class="form-label">选择包厢:</label>';
        rooms.forEach(room => {
            html += `<button type="button" class="btn btn-outline-primary me-2 mb-2" onclick="sendCommandToRoom('${room.trim()}', '${type}', '')">${room.trim()}</button>`;
        });
        html += '</div>';
        
        // 简单的确认框
        if (confirm(`请选择要发送"${getCommandName(type)}"指令的包厢`)) {
            showCustomCommand();
            document.getElementById('commandType').value = type;
        }
    }
    
    function sendCommandToRoom(room, type, content) {
        fetch('/waiter/send-command', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                room_number: room,
                action_type: type,
                action_content: content
            })
        }).then(response => {
            if (response.ok) {
                alert('指令发送成功！');
                location.reload();
            } else {
                alert('指令发送失败');
            }
        });
    }
    
    function showCustomCommand() {
        new bootstrap.Modal(document.getElementById('customCommandModal')).show();
    }
    
    function sendCustomCommand() {
        const room = document.getElementById('roomNumber').value;
        const type = document.getElementById('commandType').value;
        const content = document.getElementById('commandContent').value;
        
        if (!room || !type) {
            alert('请填写必填项');
            return;
        }
        
        sendCommandToRoom(room, type, content);
        bootstrap.Modal.getInstance(document.getElementById('customCommandModal')).hide();
    }
    
    function getCommandName(type) {
        const names = {
            'serve': '上菜',
            'rush': '催菜',
            'change': '换菜',
            'add_drink': '加酒水',
            'add_staple': '上主食',
            'add_fruit': '上水果',
            'special': '特殊服务'
        };
        return names[type] || type;
    }
    
    // 自动刷新页面
    setInterval(function() {
        location.reload();
    }, 60000); // 每60秒刷新一次
</script>
{% endblock %}
