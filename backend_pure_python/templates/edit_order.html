<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑订单 - 暨阳湖大酒店传菜管理系统</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .form-label {
            font-weight: 600;
            color: #495057;
        }
        .dish-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }
        .dish-item .btn-remove {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                <i class="bi bi-pencil-square"></i>
                编辑订单
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <a href="/orders" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        返回订单列表
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle"></i>
                            订单信息 - {{ order.order_number }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="/orders/{{ order.id }}/edit">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="customer_name" class="form-label">客户姓名</label>
                                        <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                               value="{{ order.customer_name or '' }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="guest_count" class="form-label">用餐人数</label>
                                        <input type="number" class="form-control" id="guest_count" name="guest_count" 
                                               value="{{ order.guest_count or 1 }}" min="1" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="dining_standard" class="form-label">用餐标准（元/桌）</label>
                                        <input type="number" class="form-control" id="dining_standard" name="dining_standard"
                                               value="{{ order.dining_standard_amount or order.subtotal or 0 }}"
                                               step="0.01" min="0" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="table_info" class="form-label">包厢信息</label>
                                        <input type="text" class="form-control" id="table_info"
                                               value="{{ order.table.number if order.table else '外带' }}" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">用餐时段 *</label>
                                <div class="row">
                                    <div class="col-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="meal_period" id="edit_breakfast" value="breakfast"
                                                   {% if order.meal_period and order.meal_period.value == 'breakfast' %}checked{% endif %}>
                                            <label class="form-check-label" for="edit_breakfast">
                                                <i class="bi bi-sunrise"></i> 早餐
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="meal_period" id="edit_lunch" value="lunch"
                                                   {% if order.meal_period and order.meal_period.value == 'lunch' %}checked{% endif %}>
                                            <label class="form-check-label" for="edit_lunch">
                                                <i class="bi bi-sun"></i> 午餐
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="meal_period" id="edit_dinner" value="dinner"
                                                   {% if not order.meal_period or order.meal_period.value == 'dinner' %}checked{% endif %}>
                                            <label class="form-check-label" for="edit_dinner">
                                                <i class="bi bi-moon"></i> 晚餐
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <small class="form-text text-muted">可以修改订单的用餐时段</small>
                            </div>

                            <div class="mb-3">
                                <label for="special_requests" class="form-label">特殊要求</label>
                                <textarea class="form-control" id="special_requests" name="special_requests" 
                                          rows="3" placeholder="请输入特殊要求...">{{ order.special_requests or '' }}</textarea>
                            </div>

                            <div class="mb-4">
                                <label for="menu_content" class="form-label">菜品列表</label>
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        每行一道菜品，可以添加、删除或修改菜品
                                    </small>
                                </div>
                                <textarea class="form-control" id="menu_content" name="menu_content" 
                                          rows="8" placeholder="请输入菜品，每行一道菜...">{% for item in order.items %}{{ item.dish_name }}
{% endfor %}</textarea>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/orders" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-x-circle"></i>
                                    取消
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i>
                                    保存修改
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-clock-history"></i>
                            订单状态
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>当前状态：</strong>
                            <span class="badge bg-{% if order.status == 'reserved' %}warning{% elif order.status == 'pending_start' %}info{% elif order.status == 'serving' %}success{% else %}secondary{% endif %}">
                                {{ order.get_status_display() }}
                            </span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>创建时间：</strong><br>
                            <small class="text-muted">{{ order.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                        </div>

                        {% if order.waiter %}
                        <div class="mb-3">
                            <strong>负责服务员：</strong><br>
                            <small class="text-muted">{{ order.waiter.full_name }}</small>
                        </div>
                        {% endif %}

                        {% if order.dining_start_time %}
                        <div class="mb-3">
                            <strong>用餐开始时间：</strong><br>
                            <small class="text-muted">{{ order.dining_start_time.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <strong>菜品数量：</strong>
                            <span class="badge bg-info">{{ order.items|length }} 道</span>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-exclamation-triangle"></i>
                            编辑说明
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success"></i>
                                可以修改客户信息和用餐标准
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success"></i>
                                可以添加、删除或修改菜品
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-info-circle text-info"></i>
                                修改后会自动重新计算订单金额
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-exclamation-circle text-warning"></i>
                                只有特定状态的订单才能编辑
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证和Ajax提交
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();

            const customerName = document.getElementById('customer_name').value.trim();
            const guestCount = parseInt(document.getElementById('guest_count').value);
            const diningStandard = parseFloat(document.getElementById('dining_standard').value);

            if (!customerName) {
                showError('请输入客户姓名');
                return;
            }

            if (guestCount < 1) {
                showError('用餐人数必须大于0');
                return;
            }

            if (diningStandard < 0) {
                showError('用餐标准不能为负数');
                return;
            }

            // 确认提交
            if (!confirm('确认保存订单修改吗？')) {
                return;
            }

            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';

            // 准备表单数据
            const formData = new FormData(this);

            // 提交表单
            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message || '订单更新成功');

                    // 可选择性跳转到订单列表
                    setTimeout(() => {
                        if (confirm('订单更新成功！是否跳转到订单管理页面？')) {
                            window.location.href = '/orders';
                        }
                    }, 2000);
                } else {
                    showError(data.message || '更新订单失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('更新订单失败，请重试');
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });

        // 菜品列表格式化
        document.getElementById('menu_content').addEventListener('blur', function() {
            const content = this.value;
            const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0);
            this.value = lines.join('\n');
        });
    </script>
</body>
</html>
