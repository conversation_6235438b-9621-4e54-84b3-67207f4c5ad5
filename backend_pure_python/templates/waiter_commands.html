{% extends "base.html" %}

{% block title %}服务员指令管理 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-megaphone"></i>
        服务员指令管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i>
                刷新
            </button>
        </div>
    </div>
</div>

<!-- 指令统计 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">今日指令</div>
                        <div class="h4">{{ today_commands_count }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-chat-dots" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">待处理</div>
                        <div class="h4">{{ pending_commands_count }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">已处理</div>
                        <div class="h4">{{ completed_commands_count }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="card-title h6">在线服务员</div>
                        <div class="h4">{{ online_waiters_count }}</div>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 指令列表 -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0">服务员指令记录</h5>
    </div>
    <div class="card-body">
        {% if commands %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>时间</th>
                        <th>包厢</th>
                        <th>服务员</th>
                        <th>指令类型</th>
                        <th>指令内容</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for command in commands %}
                    <tr>
                        <td>{{ command.created_at.strftime('%H:%M:%S') }}</td>
                        <td>
                            <span class="badge bg-primary">{{ command.room_number }}</span>
                        </td>
                        <td>{{ command.waiter.full_name if command.waiter else '未知' }}</td>
                        <td>
                            <span class="badge bg-{% if command.action_type == 'serve' %}success{% elif command.action_type == 'rush' %}warning{% elif command.action_type == 'change' %}info{% elif command.action_type == 'add_drink' %}primary{% elif command.action_type == 'special' %}danger{% else %}secondary{% endif %}">
                                {% if command.action_type == 'serve' %}上菜
                                {% elif command.action_type == 'rush' %}催菜
                                {% elif command.action_type == 'change' %}换菜
                                {% elif command.action_type == 'add_drink' %}加酒水
                                {% elif command.action_type == 'add_staple' %}上主食
                                {% elif command.action_type == 'add_fruit' %}上水果
                                {% elif command.action_type == 'special' %}特殊服务
                                {% else %}{{ command.action_type }}
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            {% if command.action_content %}
                            <small>{{ command.action_content }}</small>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if command.is_processed %}
                            <span class="badge bg-success">已处理</span>
                            {% else %}
                            <span class="badge bg-warning">待处理</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if not command.is_processed %}
                            <button type="button" class="btn btn-sm btn-outline-success" 
                                    onclick="markProcessed({{ command.id }})">
                                <i class="bi bi-check"></i>
                                标记已处理
                            </button>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-chat-dots text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">暂无指令记录</h4>
            <p class="text-muted">服务员的指令将在这里显示</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 在线服务员状态 -->
<div class="card shadow mt-4">
    <div class="card-header">
        <h5 class="mb-0">在线服务员状态</h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for waiter in online_waiters %}
            <div class="col-md-4 mb-3">
                <div class="card border-{% if waiter.is_authorized %}success{% else %}warning{% endif %}">
                    <div class="card-body">
                        <h6 class="card-title">
                            {{ waiter.full_name }}
                            {% if waiter.is_authorized %}
                            <span class="badge bg-success">已授权</span>
                            {% else %}
                            <span class="badge bg-warning">待授权</span>
                            {% endif %}
                        </h6>
                        <p class="card-text">
                            <strong>负责包厢:</strong> 
                            {% if waiter.assigned_tables %}
                            {{ waiter.assigned_tables }}
                            {% else %}
                            <span class="text-muted">未分配</span>
                            {% endif %}
                        </p>
                        <p class="card-text">
                            <small class="text-muted">
                                最后登录: {{ waiter.last_login_at.strftime('%H:%M') if waiter.last_login_at else '未知' }}
                            </small>
                        </p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    function markProcessed(commandId) {
        if (confirm('确定标记此指令为已处理吗？')) {
            fetch('/waiter/commands/' + commandId + '/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            }).then(response => {
                if (response.ok) {
                    location.reload();
                } else {
                    alert('操作失败');
                }
            });
        }
    }
    
    // 自动刷新页面
    setInterval(function() {
        location.reload();
    }, 30000); // 每30秒刷新一次
</script>
{% endblock %}
