{% extends "base.html" %}

{% block title %}打荷操作台 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 仿照厨房大屏的黑色主题设计 */
    body,
    body.theme-light,
    body.theme-dark,
    html,
    html.theme-light,
    html.theme-dark {
        background: var(--helper-bg-color, #1a1a1a) !important;
        background-color: var(--helper-bg-color, #1a1a1a) !important;
        color: var(--helper-font-color, #FFD700) !important;
    }

    /* 隐藏导航栏、侧边栏和底部继承元素 */
    .navbar,
    .sidebar,
    .offcanvas,
    .toast-container,
    .toast,
    #successToast,
    #errorToast,
    #infoToast {
        display: none !important;
    }

    .main-content,
    .main-content.theme-light,
    .main-content.theme-dark {
        background: var(--helper-bg-color, #1a1a1a) !important;
        background-color: var(--helper-bg-color, #1a1a1a) !important;
        color: var(--helper-font-color, #fff) !important;
        margin-left: 0 !important;
        width: 100% !important;
        padding: 0 !important;
    }

    .kitchen-helper {
        background: var(--helper-bg-color, #1a1a1a) !important;
        background-color: var(--helper-bg-color, #1a1a1a) !important;
        color: var(--helper-font-color, #fff) !important;
        min-height: 100vh;
        padding: 0;
        margin: 0;
        width: 100vw;
        overflow-x: hidden;
    }

    /* 仿照厨房大屏的头部布局 */
    .kitchen-helper-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: linear-gradient(135deg, #2d2d2d, #404040);
        border-bottom: 3px solid #ffc107;
        margin-bottom: 0 !important;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }

    .header-left {
        flex: 1;
        text-align: left;
    }

    .kitchen-helper-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #ffc107;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        letter-spacing: 1px;
    }

    .header-center {
        flex: 1;
        text-align: center;
    }

    .current-time-center {
        font-size: 1.3rem;
        font-weight: 600;
        color: #ffffff;
        background: rgba(0,0,0,0.3);
        padding: 6px 12px;
        border-radius: 8px;
        border: 2px solid #ffc107;
        display: inline-block;
        min-width: 180px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .header-right {
        flex: 1;
        text-align: right;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        gap: 15px;
        flex-wrap: wrap;
    }

    .status-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 5px;
    }

    .status-item {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        font-size: 0.9rem;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 6px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .status-item.pending {
        color: #28a745;
        background: rgba(40, 167, 69, 0.1);
        border: 1px solid #28a745;
    }

    .status-item.cooking {
        color: #ffc107;
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid #ffc107;
    }

    .status-item.ready {
        color: #dc3545;
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid #dc3545;
    }

    .control-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .header-right .btn {
        font-weight: 600;
        border-width: 2px;
        padding: 8px 16px;
        transition: all 0.3s ease;
        margin: 0;
    }

    .header-right .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    /* 包厢容器布局 */
    .rooms-container {
        display: grid;
        grid-template-columns: repeat(var(--rooms-per-row, 5), 1fr);
        gap: var(--room-gap, 10px);
        padding: 20px;
        height: calc(100vh - 140px);
        overflow-y: auto;
    }

    /* 包厢卡片样式 */
    .room-column {
        background: #2d2d2d;
        border-radius: 6px;
        border: var(--border-size, 2px) solid #666666;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 1px 4px rgba(0,0,0,0.3);
        min-height: 0;
        height: auto;
        max-height: calc(100vh - 160px);
    }

    .room-column.room-started {
        border-color: #28a745;
        background: #2d3d2d;
    }

    .room-header {
        font-size: 1.2rem;
        font-weight: bold;
        padding: var(--padding-top, 8px) var(--padding-left, 12px) var(--padding-bottom, 8px) var(--padding-right, 12px);
        color: #2c3e50; /* 深色字体，提高可读性 */
        text-align: center;
        border-bottom: 2px solid #ffc107;
        background: rgba(255,255,255,0.9); /* 浅色背景配合深色字体 */
    }

    .dish-items-container {
        flex: 1;
        padding: 8px;
        overflow-y: auto;
        display: grid;
        grid-template-columns: 1fr 1fr; /* 每行2个菜品 */
        gap: 8px;
    }

    /* 菜品项样式 */
    .dish-item {
        background: #3d3d3d;
        border-radius: 6px;
        padding: var(--padding-top, 8px) var(--padding-left, 12px) var(--padding-bottom, 8px) var(--padding-right, 12px);
        /* margin-bottom: 8px; 使用grid gap替代 */
        border: var(--border-size, 2px) solid #28a745;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: var(--font-size, 16px);
        color: var(--font-color, #ffffff);
    }

    .dish-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    /* 不同状态的菜品样式 */
    .dish-item.pending_cook {
        border-color: #28a745;
        background: #2d5a2d;
        color: #ffffff;
    }

    .dish-item.cooking {
        border-color: #ffc107;
        background: #5a5a2d;
        color: #000000;
        animation: pulse 2s infinite;
    }

    .dish-item.ready {
        border-color: #6c757d;
        background: #3a3a3a;
        color: #888888;
        opacity: 0.7;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .dish-name {
        font-weight: 600;
        margin-bottom: 4px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    /* 重复菜品标记 */
    .duplicate-marker {
        background: #ff6b6b;
        color: white;
        font-size: 0.7rem;
        font-weight: bold;
        padding: 2px 6px;
        border-radius: 50%;
        min-width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: 5px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        animation: duplicatePulse 2s infinite;
    }

    @keyframes duplicatePulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .dish-status {
        font-size: 0.8rem;
        opacity: 0.8;
    }

    /* 已完成菜品区域 */
    .completed-dishes-section {
        margin-top: 8px;
        border-top: 1px solid #444;
        padding-top: 8px;
    }

    .completed-dishes-header {
        font-size: 0.8rem;
        color: #888;
        text-align: center;
        margin-bottom: 4px;
        font-weight: bold;
    }

    .completed-dishes-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4px;
    }

    /* 服务员指令悬浮窗 */
    .waiter-actions-float {
        position: fixed;
        bottom: var(--float-bottom, 20px);
        right: var(--float-right, 20px);
        width: 300px;
        max-height: 400px;
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid #ffc107;
        border-radius: 10px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.5);
        z-index: 1000;
        overflow: hidden;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column-reverse; /* 标题栏在底部，内容在上方 */
        animation: slideUpFromBottom 0.3s ease-out;
    }

    .float-header {
        background: linear-gradient(135deg, #2d2d2d, #404040);
        padding: 10px 15px;
        border-top: 1px solid #ffc107; /* 改为顶部边框，因为标题栏在底部 */
        border-bottom: none;
        cursor: move;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 0 0 8px 8px; /* 只有底部圆角 */
    }

    .float-title {
        color: #ffc107;
        font-weight: bold;
        font-size: 0.9rem;
    }

    .float-controls {
        display: flex;
        gap: 5px;
    }

    .float-btn {
        background: none;
        border: 1px solid #ffc107;
        color: #ffc107;
        padding: 2px 6px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 0.8rem;
    }

    .float-btn:hover {
        background: #ffc107;
        color: #000;
    }

    .float-content {
        max-height: 300px;
        overflow-y: auto;
        padding: 10px;
        border-radius: 8px 8px 0 0; /* 只有顶部圆角，因为内容在上方 */
        flex: 1;
    }

    .action-item {
        background: rgba(255, 255, 255, 0.1);
        padding: 8px;
        margin-bottom: 6px;
        border-radius: 5px;
        border-left: 3px solid #ffc107;
    }

    .action-room {
        color: #ffc107;
        font-weight: bold;
        font-size: 1.1rem;  /* 增大字体 */
        margin-bottom: 3px;
    }

    .action-content {
        color: #fff;
        font-size: 1.0rem;  /* 增大字体 */
        font-weight: bold;  /* 加粗 */
        margin: 3px 0;
        line-height: 1.4;
    }

    .action-time {
        color: #adb5bd;
        font-size: 0.85rem;  /* 增大字体 */
    }

    .action-controls {
        margin-top: 5px;
        text-align: right;
    }

    .action-controls .btn {
        font-size: 0.7rem;
        padding: 2px 8px;
        border-radius: 3px;
    }

    /* 未开始包厢悬浮窗 */
    .pending-rooms-float {
        position: fixed;
        bottom: 20px;
        left: 20px;
        width: 300px;
        max-height: 400px;
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid #17a2b8;
        border-radius: 8px;
        z-index: 999;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        cursor: move;
        user-select: none;
        animation: slideUpFromBottom 0.3s ease-out;
        display: flex;
        flex-direction: column-reverse; /* 标题栏在底部，内容在上方 */
    }

    @keyframes slideUpFromBottom {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .pending-rooms-container {
        max-height: 300px;
        overflow-y: auto;
        padding: 10px;
        border-radius: 8px 8px 0 0; /* 只有顶部圆角 */
        flex: 1;
    }

    .pending-room-item {
        background: rgba(23, 162, 184, 0.2);
        border: 1px solid #17a2b8;
        border-radius: 5px;
        padding: 8px;
        margin-bottom: 6px;
        border-left: 3px solid #17a2b8;
    }

    .pending-room-name {
        color: #17a2b8;
        font-weight: bold;
        font-size: 1.0rem;
        margin-bottom: 2px;
    }

    .pending-waiter-name {
        color: #fff;
        font-size: 0.85rem;
        margin-bottom: 2px;
    }

    .pending-room-info {
        color: #adb5bd;
        font-size: 0.75rem;
    }

    /* 浅色主题下的未开始包厢悬浮窗 */
    .theme-light .pending-rooms-float {
        background: rgba(255, 255, 255, 0.95);
        border-color: #17a2b8;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    }

    .theme-light .pending-room-item {
        background: rgba(23, 162, 184, 0.1);
        border-color: #17a2b8;
    }

    .theme-light .pending-waiter-name {
        color: #333333;
    }

    .theme-light .pending-room-info {
        color: #6c757d;
    }

    /* 响应式设计 */
    @media (max-width: 1600px) {
        .rooms-container {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (max-width: 1200px) {
        .rooms-container {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 900px) {
        .rooms-container {
            grid-template-columns: repeat(2, 1fr);
        }

        .waiter-actions-float {
            width: 250px;
        }

        /* 小屏幕上菜品改为单列显示 */
        .dish-items-container {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 600px) {
        .rooms-container {
            grid-template-columns: 1fr;
        }

        .waiter-actions-float {
            width: 200px;
            right: 10px;
        }

        /* 小屏幕上菜品单列显示 */
        .dish-items-container {
            grid-template-columns: 1fr;
        }
    }

    /* 浅色主题 */
    .theme-light {
        --helper-bg-color: #f8f9fa;
        --helper-font-color: #333333;
    }

    .theme-light .room-column {
        background: #ffffff;
        border-color: #dee2e6;
        color: #333333;
    }

    .theme-light .dish-item {
        background: #f8f9fa;
        color: #333333;
    }

    .theme-light .waiter-actions-float {
        background: rgba(255, 255, 255, 0.95);
        color: #333333;
        border-color: #007bff;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    }

    .theme-light .float-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-bottom-color: #007bff;
    }

    .theme-light .float-title {
        color: #007bff;
    }

    .theme-light .float-btn {
        border-color: #007bff;
        color: #007bff;
    }

    .theme-light .float-btn:hover {
        background: #007bff;
        color: #fff;
    }

    .theme-light .action-item {
        background: rgba(0, 123, 255, 0.1);
        border-left-color: #007bff;
    }

    .theme-light .action-room {
        color: #007bff;
    }

    .theme-light .action-content {
        color: #333333;
        font-weight: bold;  /* 确保浅色主题下也加粗 */
    }

    .theme-light .action-time {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="kitchen-helper">
    <!-- 仿照厨房大屏的头部布局 -->
    <div class="kitchen-helper-header">
        <!-- 左侧：页面标题 -->
        <div class="header-left">
            <h1 class="kitchen-helper-title">暨阳湖大酒店打荷操作台</h1>
        </div>

        <!-- 中央：实时时间 -->
        <div class="header-center">
            <div class="current-time-center" id="currentTime"></div>
        </div>

        <!-- 右侧：状态统计和控制按钮 -->
        <div class="header-right">
            <!-- 状态统计 -->
            <div class="status-stats">
                <span class="status-item pending">
                    <i class="bi bi-clock-fill"></i>
                    待制作<span id="pendingCount">0</span>个
                </span>
                <span class="status-item cooking">
                    <i class="bi bi-arrow-clockwise"></i>
                    制作中<span id="cookingCount">0</span>个
                </span>
                <span class="status-item ready">
                    <i class="bi bi-check-circle-fill"></i>
                    已完成<span id="readyCount">0</span>个
                </span>
            </div>

            <!-- 控制按钮组 -->
            <div class="control-buttons">
                <button class="btn btn-outline-light btn-sm me-2" onclick="window.history.back()" title="返回">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
                <button class="btn btn-outline-warning btn-sm me-2" onclick="location.reload()" title="刷新">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
                <a href="/kitchen/display" class="btn btn-outline-info btn-sm" target="_blank" title="厨房大屏">
                    <i class="bi bi-display"></i> 厨房大屏
                </a>
                <a href="/dashboard" class="btn btn-outline-secondary btn-sm" title="返回主页">
                    <i class="bi bi-house"></i> 主页
                </a>
            </div>
        </div>
    </div>

    <!-- 包厢容器 -->
    <div class="rooms-container" id="roomsContainer">
        <!-- 包厢将通过JavaScript动态加载 -->
    </div>

    <!-- 服务员指令悬浮窗 -->
    <div class="waiter-actions-float" id="waiterActionsFloat">
        <div class="float-header" id="floatHeader">
            <span class="float-title">
                <i class="bi bi-megaphone-fill"></i>
                服务员指令
            </span>
            <div class="float-controls">
                <button class="float-btn" onclick="toggleFloatWindow()" title="最小化/展开">
                    <i class="bi bi-dash" id="toggleIcon"></i>
                </button>
                <button class="float-btn" onclick="adjustFloatHeight('waiter', 'increase')" title="增加高度">
                    <i class="bi bi-plus"></i>
                </button>
                <button class="float-btn" onclick="adjustFloatHeight('waiter', 'decrease')" title="减少高度">
                    <i class="bi bi-dash-lg"></i>
                </button>
                <button class="float-btn" onclick="resetFloatPosition()" title="重置位置">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
        </div>
        <div class="float-content" id="floatContent">
            <div id="waiterActionsContainer">
                <!-- 服务员指令将通过JavaScript动态加载 -->
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    暂无服务员指令
                </div>
            </div>
        </div>
    </div>

    <!-- 左下角未开始包厢悬浮窗 -->
    <div id="pendingRoomsFloat" class="pending-rooms-float">
        <div id="pendingFloatHeader" class="float-header">
            <div class="float-title">
                <i class="bi bi-clock-history"></i> 未开始包厢
            </div>
            <div class="float-controls">
                <button class="float-btn" onclick="togglePendingFloat()" title="最小化/展开">
                    <i id="pendingToggleIcon" class="bi bi-dash"></i>
                </button>
                <button class="float-btn" onclick="adjustFloatHeight('pending', 'increase')" title="增加高度">
                    <i class="bi bi-plus"></i>
                </button>
                <button class="float-btn" onclick="adjustFloatHeight('pending', 'decrease')" title="减少高度">
                    <i class="bi bi-dash-lg"></i>
                </button>
                <button class="float-btn" onclick="resetPendingFloatPosition()" title="重置位置">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
        </div>
        <div id="pendingFloatContent" class="float-content">
            <div id="pendingRoomsContainer" class="pending-rooms-container">
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    暂无未开始包厢
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div id="toastContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // 全局变量
    let kitchenHelperConfig = {};
    let roomsData = [];
    let waiterActionsData = [];
    let pendingRoomsData = [];
    let isFloatMinimized = false;
    let isPendingFloatMinimized = false;
    let isDragging = false;
    let isPendingDragging = false;
    let dragOffset = { x: 0, y: 0 };
    let pendingDragOffset = { x: 0, y: 0 };

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadKitchenHelperConfig();
        initializeFloatWindow();
        initializePendingFloatWindow();
        restoreFloatHeight(); // 恢复悬浮窗高度设置
        loadRoomsData();
        loadWaiterActions();
        loadPendingRooms();
        updateCurrentTime();

        // 定时更新
        setInterval(updateCurrentTime, 1000);
        setInterval(loadRoomsData, 5000);
        setInterval(loadWaiterActions, 3000);
        setInterval(loadPendingRooms, 5000);
    });

    // 加载未开始包厢数据
    async function loadPendingRooms() {
        try {
            const response = await fetch('/api/kitchen/pending-rooms');
            if (response.ok) {
                const data = await response.json();
                pendingRoomsData = data.rooms || [];
                renderPendingRooms();
            }
        } catch (error) {
            console.error('加载未开始包厢数据失败:', error);
        }
    }

    // 渲染未开始包厢
    function renderPendingRooms() {
        const container = document.getElementById('pendingRoomsContainer');
        if (!container) return;

        if (pendingRoomsData.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    暂无未开始包厢
                </div>
            `;
            return;
        }

        container.innerHTML = pendingRoomsData.map(room => `
            <div class="pending-room-item">
                <div class="pending-room-name">${room.room_name}</div>
                <div class="pending-waiter-name">服务员：${room.waiter_name}</div>
                <div class="pending-room-info">${room.guest_count}人 | ${room.ordered_at}</div>
            </div>
        `).join('');
    }

    // 初始化未开始包厢悬浮窗
    function initializePendingFloatWindow() {
        const floatWindow = document.getElementById('pendingRoomsFloat');
        const floatHeader = document.getElementById('pendingFloatHeader');

        if (!floatWindow || !floatHeader) return;

        // 从localStorage恢复位置
        const savedPosition = localStorage.getItem('pendingFloatPosition');
        if (savedPosition) {
            const position = JSON.parse(savedPosition);
            floatWindow.style.bottom = position.bottom + 'px';
            floatWindow.style.left = position.left + 'px';
        }

        // 拖拽功能
        floatHeader.addEventListener('mousedown', startPendingDrag);
        document.addEventListener('mousemove', pendingDrag);
        document.addEventListener('mouseup', stopPendingDrag);

        // 触摸设备支持
        floatHeader.addEventListener('touchstart', startPendingDrag);
        document.addEventListener('touchmove', pendingDrag);
        document.addEventListener('touchend', stopPendingDrag);
    }

    // 开始拖拽未开始包厢悬浮窗
    function startPendingDrag(e) {
        isPendingDragging = true;
        const floatWindow = document.getElementById('pendingRoomsFloat');
        const rect = floatWindow.getBoundingClientRect();

        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        pendingDragOffset.x = clientX - rect.left;
        pendingDragOffset.y = clientY - rect.top;

        floatWindow.style.transition = 'none';
        e.preventDefault();
    }

    // 拖拽中
    function pendingDrag(e) {
        if (!isPendingDragging) return;

        const floatWindow = document.getElementById('pendingRoomsFloat');
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        const newLeft = clientX - pendingDragOffset.x;
        const newTop = clientY - pendingDragOffset.y;

        // 边界检查
        const maxLeft = window.innerWidth - floatWindow.offsetWidth;
        const maxTop = window.innerHeight - floatWindow.offsetHeight;

        const constrainedLeft = Math.max(0, Math.min(newLeft, maxLeft));
        const constrainedTop = Math.max(0, Math.min(newTop, maxTop));

        floatWindow.style.left = constrainedLeft + 'px';
        floatWindow.style.top = constrainedTop + 'px';
        floatWindow.style.bottom = 'auto';

        e.preventDefault();
    }

    // 停止拖拽
    function stopPendingDrag() {
        if (!isPendingDragging) return;

        isPendingDragging = false;
        const floatWindow = document.getElementById('pendingRoomsFloat');
        floatWindow.style.transition = 'all 0.3s ease';

        // 保存位置
        const rect = floatWindow.getBoundingClientRect();
        const position = {
            bottom: window.innerHeight - rect.bottom,
            left: rect.left
        };
        localStorage.setItem('pendingFloatPosition', JSON.stringify(position));
    }

    // 切换未开始包厢悬浮窗显示状态
    function togglePendingFloat() {
        const floatContent = document.getElementById('pendingFloatContent');
        const toggleIcon = document.getElementById('pendingToggleIcon');

        isPendingFloatMinimized = !isPendingFloatMinimized;

        if (isPendingFloatMinimized) {
            floatContent.style.display = 'none';
            toggleIcon.className = 'bi bi-plus';
        } else {
            floatContent.style.display = 'block';
            toggleIcon.className = 'bi bi-dash';
        }
    }

    // 重置未开始包厢悬浮窗位置
    function resetPendingFloatPosition() {
        const floatWindow = document.getElementById('pendingRoomsFloat');
        floatWindow.style.bottom = '20px';
        floatWindow.style.left = '20px';
        floatWindow.style.top = 'auto';
        floatWindow.style.maxHeight = '400px'; // 重置高度

        // 清除保存的位置和高度
        localStorage.removeItem('pendingFloatPosition');
        localStorage.removeItem('pendingFloatHeight');
    }

    // 调整悬浮窗高度
    function adjustFloatHeight(type, action) {
        const floatWindow = type === 'waiter' ?
            document.getElementById('waiterActionsFloat') :
            document.getElementById('pendingRoomsFloat');

        if (!floatWindow) return;

        // 获取当前高度
        const currentHeight = parseInt(floatWindow.style.maxHeight) ||
            (type === 'waiter' ? 400 : 400);

        // 计算新高度
        let newHeight = currentHeight;
        if (action === 'increase') {
            newHeight = Math.min(currentHeight + 50, 800); // 最大800px
        } else if (action === 'decrease') {
            newHeight = Math.max(currentHeight - 50, 200); // 最小200px
        }

        // 应用新高度
        floatWindow.style.maxHeight = newHeight + 'px';

        // 保存到localStorage
        const storageKey = type === 'waiter' ? 'waiterFloatHeight' : 'pendingFloatHeight';
        localStorage.setItem(storageKey, newHeight.toString());

        console.log(`${type}悬浮窗高度调整为: ${newHeight}px`);
    }

    // 恢复悬浮窗高度设置
    function restoreFloatHeight() {
        // 恢复服务员指令悬浮窗高度
        const waiterHeight = localStorage.getItem('waiterFloatHeight');
        if (waiterHeight) {
            const waiterFloat = document.getElementById('waiterActionsFloat');
            if (waiterFloat) {
                waiterFloat.style.maxHeight = waiterHeight + 'px';
            }
        }

        // 恢复未开始包厢悬浮窗高度
        const pendingHeight = localStorage.getItem('pendingFloatHeight');
        if (pendingHeight) {
            const pendingFloat = document.getElementById('pendingRoomsFloat');
            if (pendingFloat) {
                pendingFloat.style.maxHeight = pendingHeight + 'px';
            }
        }
    }

    // 计算菜品重复次数
    function calculateDishCounts() {
        const dishCounts = {};

        // 遍历所有包厢的所有菜品
        roomsData.forEach(room => {
            const allDishes = [
                ...(room.pending_dishes || []),
                ...(room.ready_dishes || [])
            ];

            allDishes.forEach(dish => {
                const dishName = dish.name;
                if (!dishCounts[dishName]) {
                    dishCounts[dishName] = 0;
                }
                dishCounts[dishName]++;
            });
        });

        return dishCounts;
    }

    // 加载打荷页面配置
    async function loadKitchenHelperConfig() {
        try {
            const response = await fetch('/api/kitchen-helper-config');
            if (response.ok) {
                kitchenHelperConfig = await response.json();
                applyConfiguration();
            }
        } catch (error) {
            console.error('加载配置失败:', error);
            // 使用默认配置
            kitchenHelperConfig = {
                kitchen_helper_font_size: 16,
                kitchen_helper_font_color: '#ffffff',
                kitchen_helper_theme: 'dark',
                kitchen_helper_border_size: 2,
                kitchen_helper_container_width: 'auto',
                kitchen_helper_padding_top: 8,
                kitchen_helper_padding_bottom: 8,
                kitchen_helper_padding_left: 12,
                kitchen_helper_padding_right: 12,
                kitchen_helper_rooms_per_row: 5,
                kitchen_helper_room_gap: 10
            };
            applyConfiguration();
        }
    }

    // 应用配置
    function applyConfiguration() {
        const root = document.documentElement;

        // 应用CSS变量
        root.style.setProperty('--font-size', kitchenHelperConfig.kitchen_helper_font_size + 'px');
        root.style.setProperty('--font-color', kitchenHelperConfig.kitchen_helper_font_color);
        root.style.setProperty('--border-size', kitchenHelperConfig.kitchen_helper_border_size + 'px');
        root.style.setProperty('--padding-top', kitchenHelperConfig.kitchen_helper_padding_top + 'px');
        root.style.setProperty('--padding-bottom', kitchenHelperConfig.kitchen_helper_padding_bottom + 'px');
        root.style.setProperty('--padding-left', kitchenHelperConfig.kitchen_helper_padding_left + 'px');
        root.style.setProperty('--padding-right', kitchenHelperConfig.kitchen_helper_padding_right + 'px');
        root.style.setProperty('--rooms-per-row', kitchenHelperConfig.kitchen_helper_rooms_per_row);
        root.style.setProperty('--room-gap', kitchenHelperConfig.kitchen_helper_room_gap + 'px');

        // 应用主题
        if (kitchenHelperConfig.kitchen_helper_theme === 'light') {
            document.body.classList.add('theme-light');
            root.style.setProperty('--helper-bg-color', '#f8f9fa');
            root.style.setProperty('--helper-font-color', '#333333');
        } else {
            document.body.classList.add('theme-dark');
            root.style.setProperty('--helper-bg-color', '#1a1a1a');
            root.style.setProperty('--helper-font-color', '#ffffff');
        }
    }

    // 更新当前时间
    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });

        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    // 加载包厢数据
    async function loadRoomsData() {
        try {
            const response = await fetch('/api/kitchen/rooms-status', {
                credentials: 'include' // 包含cookies进行认证
            });
            if (response.ok) {
                const data = await response.json();
                roomsData = data.rooms || [];
                renderRooms();
                updateStatistics();
            }
        } catch (error) {
            console.error('加载包厢数据失败:', error);
        }
    }

    // 渲染包厢
    function renderRooms() {
        const container = document.getElementById('roomsContainer');
        if (!container) return;

        if (roomsData.length === 0) {
            container.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #888;">
                    <i class="bi bi-info-circle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h3>暂无用餐中的包厢</h3>
                    <p>当前没有包厢开始用餐，请等待服务员开始用餐操作</p>
                </div>
            `;
            return;
        }

        // 计算菜品重复次数
        const dishCounts = calculateDishCounts();

        container.innerHTML = roomsData.map(room => {
            const pendingDishes = room.pending_dishes || [];
            const readyDishes = room.ready_dishes || [];
            // 注意：简化版本没有cooking状态，只有pending_cook和ready

            return `
                <div class="room-column room-started">
                    <div class="room-header">
                        ${room.room_name}
                        <small style="display: block; font-size: 0.8rem; opacity: 0.8;">
                            ${room.guest_count}人 | ${room.dining_start_time || ''}
                        </small>
                    </div>
                    <div class="dish-items-container">
                        ${pendingDishes.map(dish => {
                            const dishCount = dishCounts[dish.name] || 1;
                            const duplicateMarker = dishCount > 1 ? `<span class="duplicate-marker">${dishCount}</span>` : '';
                            return `
                                <div class="dish-item pending_cook" onclick="confirmDishCompletion(${dish.id}, '${dish.name}')">
                                    <div class="dish-name">
                                        ${dish.name}
                                        ${duplicateMarker}
                                    </div>
                                    <div class="dish-status">点击标记完成</div>
                                </div>
                            `;
                        }).join('')}

                        ${readyDishes.length > 0 ? `
                            <div class="completed-dishes-section">
                                <div class="completed-dishes-header">已完成菜品</div>
                                <div class="completed-dishes-grid">
                                    ${readyDishes.map(dish => {
                                        const dishCount = dishCounts[dish.name] || 1;
                                        const duplicateMarker = dishCount > 1 ? `<span class="duplicate-marker">${dishCount}</span>` : '';
                                        return `
                                            <div class="dish-item ready">
                                                <div class="dish-name">
                                                    ${dish.name}
                                                    ${duplicateMarker}
                                                </div>
                                                <div class="dish-status">已完成</div>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    // 更新统计信息
    function updateStatistics() {
        let pendingCount = 0;
        let readyCount = 0;

        roomsData.forEach(room => {
            pendingCount += (room.pending_dishes || []).length;
            readyCount += (room.ready_dishes || []).length;
        });

        // 更新头部统计（简化版本没有cooking状态）
        const pendingElement = document.getElementById('pendingCount');
        const cookingElement = document.getElementById('cookingCount');
        const readyElement = document.getElementById('readyCount');

        if (pendingElement) pendingElement.textContent = pendingCount;
        if (cookingElement) cookingElement.textContent = 0; // 简化版本没有cooking状态
        if (readyElement) readyElement.textContent = readyCount;
    }

    // 确认菜品完成
    function confirmDishCompletion(dishId, dishName) {
        const confirmed = confirm(`确定要标记"${dishName}"为完成状态吗？`);
        if (confirmed) {
            changeDishStatus(dishId, 'ready');
        }
    }

    // 改变菜品状态
    async function changeDishStatus(dishId, newStatus) {
        try {
            // 在更新状态前，先获取菜品信息用于通知
            let dishInfo = null;
            if (newStatus === 'ready') {
                dishInfo = findDishInfo(dishId);
                console.log(`🔍 查找菜品信息: dishId=${dishId}, dishInfo=`, dishInfo);
            }

            const response = await fetch('/api/kitchen/dish-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include', // 包含cookies进行认证
                body: JSON.stringify({
                    dish_id: dishId,
                    status: newStatus
                })
            });

            if (response.ok) {
                console.log(`✅ 菜品状态更新成功: ${dishId} -> ${newStatus}`);

                // 如果是标记完成，发送厨房大屏通知
                if (newStatus === 'ready' && dishInfo) {
                    console.log(`📢 准备发送菜品完成通知:`, dishInfo);
                    await sendDishCompletionNotification(dishInfo);
                } else if (newStatus === 'ready' && !dishInfo) {
                    console.error(`❌ 无法找到菜品信息，无法发送通知: dishId=${dishId}`);
                }

                // 立即刷新数据
                await loadRoomsData();
            } else {
                const error = await response.json();
                console.error('状态更新失败:', error);
                alert('状态更新失败: ' + (error.detail || '未知错误'));
            }
        } catch (error) {
            console.error('更新菜品状态失败:', error);
            alert('网络错误，请重试');
        }
    }

    // 查找菜品信息
    function findDishInfo(dishId) {
        for (const room of roomsData) {
            const allDishes = [
                ...(room.pending_dishes || []),
                ...(room.ready_dishes || [])
            ];

            for (const dish of allDishes) {
                if (dish.id === dishId) {
                    return {
                        dishName: dish.name,
                        roomName: room.room_name
                    };
                }
            }
        }
        return null;
    }

    // 发送菜品完成通知到厨房大屏
    async function sendDishCompletionNotification(dishInfo) {
        try {
            const notificationData = {
                type: 'dish_completion',
                message: `${dishInfo.roomName}${dishInfo.dishName}，跑菜`,
                roomName: dishInfo.roomName,
                dishName: dishInfo.dishName,
                timestamp: new Date().toISOString()
            };

            console.log(`📤 发送菜品完成通知数据:`, notificationData);

            // 发送到后端，由后端通过WebSocket广播到厨房大屏
            const response = await fetch('/api/kitchen/dish-completion-notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include', // 包含cookies进行认证
                body: JSON.stringify(notificationData)
            });

            console.log(`📡 API响应状态: ${response.status}`);

            if (response.ok) {
                const result = await response.json();
                console.log(`📢 菜品完成通知已发送: ${dishInfo.roomName}${dishInfo.dishName}`, result);
            } else {
                const error = await response.text();
                console.error('发送菜品完成通知失败:', response.status, error);
            }
        } catch (error) {
            console.error('发送菜品完成通知失败:', error);
        }
    }

    // 加载服务员指令
    async function loadWaiterActions() {
        try {
            const response = await fetch('/api/waiter-actions/latest', {
                credentials: 'include' // 包含cookies进行认证
            });
            if (response.ok) {
                const data = await response.json();
                waiterActionsData = data.actions || [];
                renderWaiterActions();
            }
        } catch (error) {
            console.error('加载服务员指令失败:', error);
        }
    }

    // 渲染服务员指令
    function renderWaiterActions() {
        const container = document.getElementById('waiterActionsContainer');
        if (!container) return;

        if (waiterActionsData.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    暂无服务员指令
                </div>
            `;
            return;
        }

        // 最新指令在顶部显示
        const sortedActions = waiterActionsData.slice().sort((a, b) =>
            new Date(b.created_at) - new Date(a.created_at)
        );

        container.innerHTML = sortedActions.slice(0, 10).map(action => {
            // 格式化指令内容
            let displayContent = '';
            const waiterName = action.waiter_name || '服务员';

            // 特殊处理"开始用餐"指令
            if (action.action_type === 'start_dining' || action.action_type_display === '开始用餐') {
                // action_content直接包含人数数字，不需要正则匹配
                const count = action.action_content || '未知';
                displayContent = `[${waiterName}] 用餐开始：${count}人`;
            } else {
                // 其他指令的标准格式
                const actionText = action.action_type_display || action.action_type;
                const content = action.action_content ? '：' + action.action_content : '';
                displayContent = `[${waiterName}] ${actionText}${content}`;
            }

            return `
                <div class="action-item">
                    <div class="action-room">${action.room_number}包厢</div>
                    <div class="action-content">${displayContent}</div>
                    <div class="action-time">${formatTime(action.created_at)}</div>
                    <div class="action-controls">
                        <button class="btn btn-sm btn-success" onclick="confirmWaiterAction(${action.id})" title="确认处理">
                            <i class="bi bi-check"></i> 确认
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 初始化悬浮窗
    function initializeFloatWindow() {
        const floatWindow = document.getElementById('waiterActionsFloat');
        const floatHeader = document.getElementById('floatHeader');

        // 从localStorage恢复位置
        const savedPosition = localStorage.getItem('waiterFloatPosition');
        if (savedPosition) {
            const position = JSON.parse(savedPosition);
            floatWindow.style.bottom = position.bottom + 'px';
            floatWindow.style.right = position.right + 'px';
        }

        // 拖拽功能
        floatHeader.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', stopDrag);

        // 触摸设备支持
        floatHeader.addEventListener('touchstart', startDrag);
        document.addEventListener('touchmove', drag);
        document.addEventListener('touchend', stopDrag);
    }

    // 开始拖拽
    function startDrag(e) {
        isDragging = true;
        const floatWindow = document.getElementById('waiterActionsFloat');
        const rect = floatWindow.getBoundingClientRect();

        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        dragOffset.x = clientX - rect.left;
        dragOffset.y = clientY - rect.top;

        floatWindow.style.transition = 'none';
        e.preventDefault();
    }

    // 拖拽中
    function drag(e) {
        if (!isDragging) return;

        const floatWindow = document.getElementById('waiterActionsFloat');
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        const newLeft = clientX - dragOffset.x;
        const newTop = clientY - dragOffset.y;

        // 边界检查
        const maxLeft = window.innerWidth - floatWindow.offsetWidth;
        const maxTop = window.innerHeight - floatWindow.offsetHeight;

        const constrainedLeft = Math.max(0, Math.min(newLeft, maxLeft));
        const constrainedTop = Math.max(0, Math.min(newTop, maxTop));

        floatWindow.style.left = constrainedLeft + 'px';
        floatWindow.style.top = constrainedTop + 'px';
        floatWindow.style.right = 'auto';

        e.preventDefault();
    }

    // 停止拖拽
    function stopDrag() {
        if (!isDragging) return;

        isDragging = false;
        const floatWindow = document.getElementById('waiterActionsFloat');
        floatWindow.style.transition = 'all 0.3s ease';

        // 保存位置
        const rect = floatWindow.getBoundingClientRect();
        const position = {
            bottom: window.innerHeight - rect.bottom,
            right: window.innerWidth - rect.right
        };
        localStorage.setItem('waiterFloatPosition', JSON.stringify(position));
    }

    // 切换悬浮窗显示状态
    function toggleFloatWindow() {
        const floatContent = document.getElementById('floatContent');
        const toggleIcon = document.getElementById('toggleIcon');

        isFloatMinimized = !isFloatMinimized;

        if (isFloatMinimized) {
            floatContent.style.display = 'none';
            toggleIcon.className = 'bi bi-plus';
        } else {
            floatContent.style.display = 'block';
            toggleIcon.className = 'bi bi-dash';
        }
    }

    // 重置悬浮窗位置
    function resetFloatPosition() {
        const floatWindow = document.getElementById('waiterActionsFloat');
        floatWindow.style.bottom = '20px';
        floatWindow.style.right = '20px';
        floatWindow.style.top = 'auto';
        floatWindow.style.maxHeight = '400px'; // 重置高度

        // 清除保存的位置和高度
        localStorage.removeItem('waiterFloatPosition');
        localStorage.removeItem('waiterFloatHeight');
    }

    // 显示Toast提示
    function showToast(type, message) {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.innerHTML = `
            <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
            ${message}
        `;

        // 添加样式
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // 确认服务员指令
    async function confirmWaiterAction(actionId) {
        try {
            console.log(`🔄 正在确认指令 ID: ${actionId}`);

            const response = await fetch(`/api/waiter-actions/${actionId}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include' // 包含cookies进行认证
            });

            if (response.ok) {
                console.log(`✅ 指令 ${actionId} 确认成功`);

                // 立即刷新服务员指令列表，确保已处理的指令不再显示
                await loadWaiterActions();

                // 可选：显示简短的视觉反馈
                const button = document.querySelector(`button[onclick="confirmWaiterAction(${actionId})"]`);
                if (button) {
                    button.innerHTML = '<i class="bi bi-check-circle"></i> 已确认';
                    button.disabled = true;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-secondary');
                }
            } else {
                const error = await response.json();
                console.error(`❌ 确认指令 ${actionId} 失败:`, error);
                alert('确认失败: ' + (error.detail || '未知错误'));
            }
        } catch (error) {
            console.error('确认服务员指令失败:', error);
            alert('网络错误，请重试');
        }
    }



    // 格式化时间
    function formatTime(timeString) {
        const date = new Date(timeString);
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
</script>
{% endblock %}
