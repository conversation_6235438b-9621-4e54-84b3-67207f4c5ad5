{% extends "base.html" %}

{% block title %}等待授权 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="container-fluid h-100 d-flex align-items-center justify-content-center">
    <div class="row justify-content-center w-100">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0">
                <div class="card-body text-center p-5">
                    <!-- 等待图标 -->
                    <div class="mb-4">
                        <i class="bi bi-hourglass-split text-warning" style="font-size: 4rem;"></i>
                    </div>
                    
                    <!-- 标题 -->
                    <h3 class="card-title text-primary mb-3">等待经理授权</h3>
                    
                    <!-- 用户信息 -->
                    <div class="alert alert-info mb-4">
                        <h5 class="mb-2">
                            <i class="bi bi-person-circle"></i> {{ user.full_name }}
                        </h5>
                        <p class="mb-0">
                            <span class="badge bg-secondary">{{ user.role.value }}</span>
                        </p>
                    </div>
                    
                    <!-- 说明信息 -->
                    <div class="mb-4">
                        <p class="text-muted mb-3">
                            您的账号已登录成功，但还未被餐饮经理授权使用包厢服务功能。
                        </p>
                        <p class="text-muted mb-3">
                            请联系餐饮经理为您分配包厢并授权访问权限。
                        </p>
                    </div>
                    
                    <!-- 授权状态 -->
                    <div class="alert alert-warning mb-4">
                        <h6><i class="bi bi-exclamation-triangle"></i> 授权状态</h6>
                        <p class="mb-2">
                            <strong>当前状态：</strong>
                            <span class="badge bg-warning text-dark">未授权</span>
                        </p>
                        <p class="mb-0">
                            <strong>分配包厢：</strong>
                            {% if user.assigned_tables %}
                                <span class="badge bg-info">{{ user.assigned_tables }}</span>
                            {% else %}
                                <span class="text-muted">暂无分配</span>
                            {% endif %}
                        </p>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" onclick="checkAuthStatus()">
                            <i class="bi bi-arrow-clockwise"></i> 检查授权状态
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="logout()">
                            <i class="bi bi-box-arrow-right"></i> 退出登录
                        </button>
                    </div>
                    
                    <!-- 帮助信息 -->
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            如有疑问，请联系餐饮经理或系统管理员
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 自动刷新检查授权状态 -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050">
    <div id="authCheckToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="bi bi-info-circle text-primary me-2"></i>
            <strong class="me-auto">授权检查</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            正在检查授权状态...
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let authCheckInterval;
    
    // 检查授权状态
    function checkAuthStatus() {
        showToast('正在检查授权状态...');
        
        fetch('/api/check-auth-status', {
            method: 'GET',
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.is_authorized) {
                showToast('授权成功！正在跳转...', 'success');
                setTimeout(() => {
                    window.location.href = '/waiter/menu';
                }, 1500);
            } else {
                showToast('仍未授权，请等待经理授权', 'warning');
            }
        })
        .catch(error => {
            console.error('检查授权状态失败:', error);
            showToast('检查失败，请重试', 'error');
        });
    }
    
    // 显示提示消息
    function showToast(message, type = 'info') {
        const toastElement = document.getElementById('authCheckToast');
        const toastBody = toastElement.querySelector('.toast-body');
        const toastHeader = toastElement.querySelector('.toast-header i');
        
        toastBody.textContent = message;
        
        // 根据类型设置图标和颜色
        toastHeader.className = 'me-2 bi ';
        switch(type) {
            case 'success':
                toastHeader.className += 'bi-check-circle text-success';
                break;
            case 'warning':
                toastHeader.className += 'bi-exclamation-triangle text-warning';
                break;
            case 'error':
                toastHeader.className += 'bi-x-circle text-danger';
                break;
            default:
                toastHeader.className += 'bi-info-circle text-primary';
        }
        
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
    }
    
    // 页面加载时开始自动检查
    document.addEventListener('DOMContentLoaded', function() {
        // 立即检查一次
        setTimeout(checkAuthStatus, 1000);
        
        // 每30秒自动检查一次
        authCheckInterval = setInterval(checkAuthStatus, 30000);
    });
    
    // 页面卸载时清除定时器
    window.addEventListener('beforeunload', function() {
        if (authCheckInterval) {
            clearInterval(authCheckInterval);
        }
    });
</script>
{% endblock %}
