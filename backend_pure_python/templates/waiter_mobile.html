{% extends "base.html" %}

{% block title %}服务员操作 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 手机端大按钮样式 */
    .mobile-btn {
        height: 80px;
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 15px;
        border-radius: 15px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .mobile-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .mobile-btn i {
        font-size: 1.5rem;
        margin-right: 10px;
    }
    
    .room-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 20px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .dish-list {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .dish-item {
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .dish-item:last-child {
        border-bottom: none;
    }
    
    .dish-item.served {
        text-decoration: line-through;
        color: #6c757d;
        background: #e9ecef;
    }
    
    .dish-status {
        font-size: 0.8rem;
        padding: 2px 8px;
        border-radius: 10px;
        margin-left: 10px;
    }
    
    @media (max-width: 768px) {
        .mobile-btn {
            height: 70px;
            font-size: 1.1rem;
        }
        
        .room-header h2 {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    {% if assigned_menu %}
    <!-- 包厢信息 -->
    <div class="room-header">
        <h2><i class="bi bi-house-door"></i> {{ assigned_menu.room_number }}</h2>
        <p class="mb-1">{{ assigned_menu.customer_name or '客户' }} | {{ assigned_menu.guest_count }}人</p>
        <small>{{ assigned_menu.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
    </div>
    
    <!-- 操作按钮 -->
    <div class="row mb-4">
        <div class="col-6">
            <button type="button" class="btn btn-success mobile-btn w-100" onclick="serveAction()">
                <i class="bi bi-arrow-up-circle"></i>
                上菜
            </button>
        </div>
        <div class="col-6">
            <button type="button" class="btn btn-warning mobile-btn w-100" onclick="rushAction()">
                <i class="bi bi-clock-history"></i>
                催菜
            </button>
        </div>
        <div class="col-6">
            <button type="button" class="btn btn-info mobile-btn w-100" onclick="changeAction()">
                <i class="bi bi-arrow-repeat"></i>
                换菜
            </button>
        </div>
        <div class="col-6">
            <button type="button" class="btn btn-primary mobile-btn w-100" onclick="addDrinkAction()">
                <i class="bi bi-cup-straw"></i>
                加酒水饮料
            </button>
        </div>
        <div class="col-6">
            <button type="button" class="btn btn-secondary mobile-btn w-100" onclick="addStapleAction()">
                <i class="bi bi-bowl"></i>
                上主食
            </button>
        </div>
        <div class="col-6">
            <button type="button" class="btn btn-danger mobile-btn w-100" onclick="addFruitAction()">
                <i class="bi bi-apple"></i>
                上水果
            </button>
        </div>
        <div class="col-12">
            <button type="button" class="btn btn-dark mobile-btn w-100" onclick="specialAction()">
                <i class="bi bi-star"></i>
                特殊服务（如澳龙泡饭）
            </button>
        </div>
    </div>
    
    <!-- 菜品列表 -->
    <div class="dish-list">
        <h5><i class="bi bi-list-ul"></i> 菜品列表</h5>
        {% for dish in assigned_menu.dish_items %}
        <div class="dish-item {% if dish.is_served %}served{% endif %}">
            <div class="flex-grow-1">
                <strong>{{ dish.dish_name }}</strong>
                {% if dish.is_served %}
                <span class="dish-status bg-success text-white">已上菜</span>
                {% elif dish.is_rushed %}
                <span class="dish-status bg-warning text-dark">已催菜</span>
                {% elif dish.is_changed %}
                <span class="dish-status bg-info text-white">已换菜</span>
                {% else %}
                <span class="dish-status bg-light text-dark">待制作</span>
                {% endif %}
            </div>
            <div>
                {% if dish.served_at %}
                <small class="text-muted">{{ dish.served_at.strftime('%H:%M') }}</small>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
    
    {% else %}
    <!-- 未分配包厢 -->
    <div class="text-center py-5">
        <i class="bi bi-house-x" style="font-size: 4rem; color: #6c757d;"></i>
        <h3 class="text-muted mt-3">未分配包厢</h3>
        <p class="text-muted">请联系餐饮经理为您分配包厢</p>
        <button type="button" class="btn btn-primary" onclick="location.reload()">
            <i class="bi bi-arrow-clockwise"></i>
            刷新页面
        </button>
    </div>
    {% endif %}
</div>

<!-- 操作模态框 -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalTitle">操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="actionModalContent">
                    <!-- 动态内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAction">确认</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    let currentAction = '';
    let currentMenuId = {{ assigned_menu.id if assigned_menu else 'null' }};
    
    function serveAction() {
        currentAction = 'serve';
        document.getElementById('actionModalTitle').textContent = '上菜确认';
        document.getElementById('actionModalContent').innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                确认为 <strong>{{ assigned_menu.room_number if assigned_menu else '' }}</strong> 包厢上菜？
            </div>
            <p>此操作将通知厨房打荷，客人已到齐可以开始上菜。</p>
        `;
        new bootstrap.Modal(document.getElementById('actionModal')).show();
    }
    
    function rushAction() {
        currentAction = 'rush';
        document.getElementById('actionModalTitle').textContent = '催菜';
        document.getElementById('actionModalContent').innerHTML = `
            <div class="mb-3">
                <label for="rushNote" class="form-label">催菜原因</label>
                <textarea class="form-control" id="rushNote" rows="3" placeholder="请输入催菜原因..."></textarea>
            </div>
        `;
        new bootstrap.Modal(document.getElementById('actionModal')).show();
    }
    
    function changeAction() {
        currentAction = 'change';
        document.getElementById('actionModalTitle').textContent = '换菜';
        document.getElementById('actionModalContent').innerHTML = `
            <div class="mb-3">
                <label for="changeNote" class="form-label">换菜要求</label>
                <textarea class="form-control" id="changeNote" rows="3" placeholder="请输入换菜要求..."></textarea>
            </div>
        `;
        new bootstrap.Modal(document.getElementById('actionModal')).show();
    }
    
    function addDrinkAction() {
        currentAction = 'add_drink';
        document.getElementById('actionModalTitle').textContent = '加酒水饮料';
        document.getElementById('actionModalContent').innerHTML = `
            <div class="mb-3">
                <label for="drinkNote" class="form-label">酒水饮料</label>
                <textarea class="form-control" id="drinkNote" rows="3" placeholder="请输入需要添加的酒水饮料..."></textarea>
            </div>
        `;
        new bootstrap.Modal(document.getElementById('actionModal')).show();
    }
    
    function addStapleAction() {
        currentAction = 'add_staple';
        document.getElementById('actionModalTitle').textContent = '上主食';
        document.getElementById('actionModalContent').innerHTML = `
            <div class="mb-3">
                <label for="stapleNote" class="form-label">主食</label>
                <textarea class="form-control" id="stapleNote" rows="3" placeholder="请输入需要添加的主食..."></textarea>
            </div>
        `;
        new bootstrap.Modal(document.getElementById('actionModal')).show();
    }
    
    function addFruitAction() {
        currentAction = 'add_fruit';
        document.getElementById('actionModalTitle').textContent = '上水果';
        document.getElementById('actionModalContent').innerHTML = `
            <div class="mb-3">
                <label for="fruitNote" class="form-label">水果</label>
                <textarea class="form-control" id="fruitNote" rows="3" placeholder="请输入需要添加的水果..."></textarea>
            </div>
        `;
        new bootstrap.Modal(document.getElementById('actionModal')).show();
    }
    
    function specialAction() {
        currentAction = 'special';
        document.getElementById('actionModalTitle').textContent = '特殊服务';
        document.getElementById('actionModalContent').innerHTML = `
            <div class="mb-3">
                <label for="specialNote" class="form-label">特殊服务要求</label>
                <textarea class="form-control" id="specialNote" rows="3" placeholder="请输入特殊服务要求，如澳龙泡饭等..."></textarea>
            </div>
        `;
        new bootstrap.Modal(document.getElementById('actionModal')).show();
    }
    
    // 确认操作
    document.getElementById('confirmAction').addEventListener('click', function() {
        if (!currentMenuId) {
            alert('未分配包厢，无法操作');
            return;
        }
        
        let content = '';
        const noteElement = document.querySelector('#actionModalContent textarea');
        if (noteElement) {
            content = noteElement.value;
        }
        
        // 发送请求
        fetch('/waiter/action', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                menu_id: currentMenuId,
                action_type: currentAction,
                content: content
            })
        }).then(response => {
            if (response.ok) {
                bootstrap.Modal.getInstance(document.getElementById('actionModal')).hide();
                location.reload();
            } else {
                alert('操作失败');
            }
        });
    });
    
    // 自动刷新页面
    setInterval(function() {
        location.reload();
    }, 60000); // 每分钟刷新一次
</script>
{% endblock %}
