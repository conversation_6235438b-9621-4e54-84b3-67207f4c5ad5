{% extends "base.html" %}

{% block title %}新建订单 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-plus-circle"></i>
        新建订单
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="/orders" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i>
                返回订单列表
            </a>
        </div>
    </div>
</div>

<form method="post" action="/orders/create">
    <div class="row">
        <!-- 订单基本信息 -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i>
                        订单信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="table_id" class="form-label">选择包厢 *</label>
                        <select class="form-select" id="table_id" name="table_id" required>
                            <option value="">请选择包厢</option>
                            {% for table in tables %}
                            <option value="{{ table.id }}" 
                                    {% if request.query_params.get('table_id') == table.id|string %}selected{% endif %}>
                                {{ table.number }} - {{ table.name or '' }} 
                                ({{ table.capacity }}人)
                                {% if table.table_type.value == 'vip_room' %} [VIP]{% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="customer_name" class="form-label">客户姓名</label>
                        <input type="text" class="form-control" id="customer_name" name="customer_name" 
                               placeholder="客户姓名（可选）">
                    </div>
                    
                    <div class="mb-3">
                        <label for="customer_phone" class="form-label">联系电话</label>
                        <input type="tel" class="form-control" id="customer_phone" name="customer_phone" 
                               placeholder="联系电话（可选）">
                    </div>
                    
                    <div class="mb-3">
                        <label for="guest_count" class="form-label">客人数量 *</label>
                        <input type="number" class="form-control" id="guest_count" name="guest_count"
                               value="1" min="1" max="50" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">用餐时段 *</label>
                        <div class="row">
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="meal_period" id="breakfast" value="breakfast">
                                    <label class="form-check-label" for="breakfast">
                                        <i class="bi bi-sunrise"></i> 早餐
                                    </label>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="meal_period" id="lunch" value="lunch">
                                    <label class="form-check-label" for="lunch">
                                        <i class="bi bi-sun"></i> 午餐
                                    </label>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="meal_period" id="dinner" value="dinner" checked>
                                    <label class="form-check-label" for="dinner">
                                        <i class="bi bi-moon"></i> 晚餐
                                    </label>
                                </div>
                            </div>
                        </div>
                        <small class="form-text text-muted">默认选择晚餐时段</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="special_requests" class="form-label">特殊要求</label>
                        <textarea class="form-control" id="special_requests" name="special_requests" 
                                  rows="3" placeholder="特殊要求或备注"></textarea>
                    </div>
                </div>
            </div>
            
            <!-- 订单汇总 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calculator"></i>
                        订单汇总
                    </h5>
                </div>
                <div class="card-body">
                    <div id="order-summary">
                        <div class="text-center text-muted py-3">
                            <i class="bi bi-cart" style="font-size: 2rem;"></i>
                            <p class="mt-2">请选择菜品</p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <span>小计:</span>
                        <span id="subtotal">¥0.00</span>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <span>服务费:</span>
                        <span id="service-charge">¥0.00</span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between fw-bold">
                        <span>总计:</span>
                        <span id="total" class="text-primary">¥0.00</span>
                    </div>
                    
                    <div class="d-grid gap-2 mt-3">
                        <button type="submit" class="btn btn-primary" id="submit-btn" disabled>
                            <i class="bi bi-check-circle"></i>
                            确认下单
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearOrder()">
                            <i class="bi bi-arrow-clockwise"></i>
                            清空订单
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 菜品选择 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-book"></i>
                        选择菜品
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 菜品分类标签 -->
                    <div class="mb-3">
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="category-filter" id="all-categories" value="" checked>
                            <label class="btn btn-outline-primary" for="all-categories">全部</label>
                            
                            <input type="radio" class="btn-check" name="category-filter" id="cold-dish" value="cold_dish">
                            <label class="btn btn-outline-primary" for="cold-dish">凉菜</label>
                            
                            <input type="radio" class="btn-check" name="category-filter" id="hot-dish" value="hot_dish">
                            <label class="btn btn-outline-primary" for="hot-dish">热菜</label>
                            
                            <input type="radio" class="btn-check" name="category-filter" id="soup" value="soup">
                            <label class="btn btn-outline-primary" for="soup">汤羹</label>
                            
                            <input type="radio" class="btn-check" name="category-filter" id="staple" value="staple">
                            <label class="btn btn-outline-primary" for="staple">主食</label>
                            
                            <input type="radio" class="btn-check" name="category-filter" id="beverage" value="beverage">
                            <label class="btn btn-outline-primary" for="beverage">饮品</label>
                        </div>
                    </div>
                    
                    <!-- 菜品搜索 -->
                    <div class="mb-3">
                        <input type="text" class="form-control" id="dish-search" placeholder="搜索菜品名称...">
                    </div>
                    
                    <!-- 菜品列表 -->
                    <div class="row" id="dishes-container">
                        {% for dish in dishes %}
                        <div class="col-md-6 col-lg-4 mb-3 dish-item" data-category="{{ dish.category.value }}">
                            <div class="card h-100">
                                {% if dish.image_url %}
                                <img src="{{ dish.image_url }}" class="card-img-top" alt="{{ dish.name }}" 
                                     style="height: 150px; object-fit: cover;">
                                {% else %}
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                     style="height: 150px;">
                                    <i class="bi bi-image text-muted" style="font-size: 2rem;"></i>
                                </div>
                                {% endif %}
                                
                                <div class="card-body p-2">
                                    <h6 class="card-title mb-1">
                                        {{ dish.name }}
                                        {% if dish.is_signature %}
                                        <span class="badge bg-warning"><i class="bi bi-star"></i></span>
                                        {% endif %}
                                        {% if dish.is_recommended %}
                                        <span class="badge bg-primary">推荐</span>
                                        {% endif %}
                                        {% if dish.is_new %}
                                        <span class="badge bg-success">新品</span>
                                        {% endif %}
                                    </h6>
                                    
                                    {% if dish.description %}
                                    <p class="card-text small text-muted mb-2">{{ dish.description[:50] }}{% if dish.description|length > 50 %}...{% endif %}</p>
                                    {% endif %}
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="fw-bold text-primary">¥{{ "%.2f"|format(dish.price) }}</span>
                                            {% if dish.spicy_level.value != 'none' %}
                                            <br><small class="text-danger">
                                                {% for i in range((dish.spicy_level.value == 'mild' and 1) or (dish.spicy_level.value == 'medium' and 2) or (dish.spicy_level.value == 'hot' and 3) or (dish.spicy_level.value == 'extra_hot' and 4) or 0) %}🌶{% endfor %}
                                            </small>
                                            {% endif %}
                                        </div>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-secondary" 
                                                    onclick="decreaseDish({{ dish.id }})">-</button>
                                            <input type="number" class="form-control text-center" 
                                                   id="dish_{{ dish.id }}" name="dish_{{ dish.id }}" 
                                                   value="0" min="0" max="99" style="width: 60px;"
                                                   onchange="updateDish({{ dish.id }}, '{{ dish.name }}', {{ dish.price }})">
                                            <button type="button" class="btn btn-outline-secondary" 
                                                    onclick="increaseDish({{ dish.id }})">+</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

{% endblock %}

{% block extra_js %}
<script>
    let orderItems = {};
    let serviceChargeRate = 0;
    
    // 菜品分类筛选
    document.querySelectorAll('input[name="category-filter"]').forEach(radio => {
        radio.addEventListener('change', function() {
            filterDishes();
        });
    });
    
    // 菜品搜索
    document.getElementById('dish-search').addEventListener('input', function() {
        filterDishes();
    });
    
    function filterDishes() {
        const category = document.querySelector('input[name="category-filter"]:checked').value;
        const search = document.getElementById('dish-search').value.toLowerCase();
        
        document.querySelectorAll('.dish-item').forEach(item => {
            const itemCategory = item.dataset.category;
            const itemName = item.querySelector('.card-title').textContent.toLowerCase();
            
            const categoryMatch = !category || itemCategory === category;
            const searchMatch = !search || itemName.includes(search);
            
            item.style.display = categoryMatch && searchMatch ? 'block' : 'none';
        });
    }
    
    function increaseDish(dishId) {
        const input = document.getElementById('dish_' + dishId);
        const currentValue = parseInt(input.value) || 0;
        input.value = currentValue + 1;
        
        const dishName = input.closest('.dish-item').querySelector('.card-title').textContent.trim();
        const dishPrice = parseFloat(input.closest('.card-body').querySelector('.text-primary').textContent.replace('¥', ''));
        
        updateDish(dishId, dishName, dishPrice);
    }
    
    function decreaseDish(dishId) {
        const input = document.getElementById('dish_' + dishId);
        const currentValue = parseInt(input.value) || 0;
        if (currentValue > 0) {
            input.value = currentValue - 1;
            
            const dishName = input.closest('.dish-item').querySelector('.card-title').textContent.trim();
            const dishPrice = parseFloat(input.closest('.card-body').querySelector('.text-primary').textContent.replace('¥', ''));
            
            updateDish(dishId, dishName, dishPrice);
        }
    }
    
    function updateDish(dishId, dishName, dishPrice) {
        const quantity = parseInt(document.getElementById('dish_' + dishId).value) || 0;
        
        if (quantity > 0) {
            orderItems[dishId] = {
                name: dishName,
                price: dishPrice,
                quantity: quantity,
                total: dishPrice * quantity
            };
        } else {
            delete orderItems[dishId];
        }
        
        updateOrderSummary();
    }
    
    function updateOrderSummary() {
        const summaryContainer = document.getElementById('order-summary');
        const subtotalElement = document.getElementById('subtotal');
        const serviceChargeElement = document.getElementById('service-charge');
        const totalElement = document.getElementById('total');
        const submitBtn = document.getElementById('submit-btn');
        
        if (Object.keys(orderItems).length === 0) {
            summaryContainer.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="bi bi-cart" style="font-size: 2rem;"></i>
                    <p class="mt-2">请选择菜品</p>
                </div>
            `;
            subtotalElement.textContent = '¥0.00';
            serviceChargeElement.textContent = '¥0.00';
            totalElement.textContent = '¥0.00';
            submitBtn.disabled = true;
            return;
        }
        
        let html = '<div class="list-group list-group-flush">';
        let subtotal = 0;
        
        for (const [dishId, item] of Object.entries(orderItems)) {
            html += `
                <div class="list-group-item px-0 py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fw-bold">${item.name}</div>
                            <small class="text-muted">¥${item.price.toFixed(2)} × ${item.quantity}</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">¥${item.total.toFixed(2)}</div>
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    onclick="removeDish(${dishId})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            subtotal += item.total;
        }
        html += '</div>';
        
        summaryContainer.innerHTML = html;
        
        const serviceCharge = subtotal * serviceChargeRate / 100;
        const total = subtotal + serviceCharge;
        
        subtotalElement.textContent = '¥' + subtotal.toFixed(2);
        serviceChargeElement.textContent = '¥' + serviceCharge.toFixed(2);
        totalElement.textContent = '¥' + total.toFixed(2);
        
        submitBtn.disabled = false;
    }
    
    function removeDish(dishId) {
        document.getElementById('dish_' + dishId).value = 0;
        delete orderItems[dishId];
        updateOrderSummary();
    }
    
    function clearOrder() {
        if (confirm('确定要清空当前订单吗？')) {
            orderItems = {};
            document.querySelectorAll('input[name^="dish_"]').forEach(input => {
                input.value = 0;
            });
            updateOrderSummary();
        }
    }
    
    // 餐桌选择变化时更新服务费率
    document.getElementById('table_id').addEventListener('change', function() {
        const tableId = this.value;
        if (tableId) {
            // 这里可以根据餐桌类型设置不同的服务费率
            // 暂时设置为固定值
            serviceChargeRate = 10; // 10%
        } else {
            serviceChargeRate = 0;
        }
        updateOrderSummary();
    });

    // 显示订单确认模态框
    function showOrderConfirmationModal(form) {
        // 获取表单数据
        const tableSelect = document.getElementById('table_id');
        const selectedTable = tableSelect.options[tableSelect.selectedIndex];
        const guestCount = document.getElementById('guest_count').value;
        const mealPeriod = document.querySelector('input[name="meal_period"]:checked');
        const customerName = document.getElementById('customer_name').value;
        const customerPhone = document.getElementById('customer_phone').value;
        const specialRequests = document.getElementById('special_requests').value;

        // 填充包厢信息
        document.getElementById('confirm-table-name').textContent = selectedTable.textContent.split(' - ')[0];
        document.getElementById('confirm-table-type').textContent = selectedTable.textContent.includes('[VIP]') ? 'VIP包厢' : '普通包厢';
        document.getElementById('confirm-guest-count').textContent = guestCount;

        // 填充用餐时段
        const mealPeriodText = {
            'breakfast': '早餐',
            'lunch': '午餐',
            'dinner': '晚餐'
        };
        document.getElementById('confirm-meal-period').textContent = mealPeriodText[mealPeriod.value] || '未选择';

        // 填充客户信息（如果有）
        if (customerName || customerPhone) {
            document.getElementById('confirm-customer-info').style.display = 'block';
            document.getElementById('confirm-customer-name').textContent = customerName || '未填写';
            document.getElementById('confirm-customer-phone').textContent = customerPhone || '未填写';
        }

        // 填充特殊要求（如果有）
        if (specialRequests) {
            document.getElementById('confirm-special-requests').style.display = 'block';
            document.getElementById('confirm-special-text').textContent = specialRequests;
        }

        // 填充菜品列表
        const dishesListContainer = document.getElementById('confirm-dishes-list');
        let dishesHtml = '';
        let subtotal = 0;

        for (const [dishId, item] of Object.entries(orderItems)) {
            dishesHtml += `
                <tr>
                    <td>${item.name}</td>
                    <td>¥${item.price.toFixed(2)}</td>
                    <td>${item.quantity}</td>
                    <td>¥${item.total.toFixed(2)}</td>
                </tr>
            `;
            subtotal += item.total;
        }
        dishesListContainer.innerHTML = dishesHtml;

        // 填充费用汇总
        const serviceCharge = subtotal * serviceChargeRate / 100;
        const total = subtotal + serviceCharge;

        document.getElementById('confirm-subtotal').textContent = '¥' + subtotal.toFixed(2);
        document.getElementById('confirm-service-charge').textContent = '¥' + serviceCharge.toFixed(2);
        document.getElementById('confirm-total').textContent = '¥' + total.toFixed(2);

        // 绑定确认按钮事件
        document.getElementById('confirm-submit-btn').onclick = function() {
            submitOrder(form);
        };

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('orderConfirmationModal'));
        modal.show();
    }

    // 提交订单
    function submitOrder(form) {
        // 隐藏模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('orderConfirmationModal'));
        modal.hide();

        // 执行原来的提交逻辑
        executeOrderSubmission(form);
    }

    // 执行订单提交
    function executeOrderSubmission(form) {
        // 防抖检查
        if (isSubmitting) {
            showError('订单正在创建中，请勿重复提交');
            return false;
        }

        // 设置提交状态
        isSubmitting = true;

        const submitBtn = document.getElementById('submit-btn');
        const originalText = submitBtn.innerHTML;

        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 创建中...';

        // 设置超时保护（30秒后自动恢复）
        submitTimeout = setTimeout(() => {
            isSubmitting = false;
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
            showError('提交超时，请重试');
        }, 30000);

        // 准备表单数据
        const formData = new FormData(form);

        // 提交订单
        fetch('/orders/create', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // 清除超时保护
            if (submitTimeout) {
                clearTimeout(submitTimeout);
                submitTimeout = null;
            }

            if (data.success) {
                // 显示详细的成功Toast通知
                showOrderCreatedToast(data);

                // 延迟跳转到订单详情页面
                setTimeout(() => {
                    window.location.href = `/orders/${data.order_id}`;
                }, 2000);
            } else {
                // 恢复提交状态
                isSubmitting = false;
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;

                showError(data.message || '创建订单失败');
            }
        })
        .catch(error => {
            // 清除超时保护
            if (submitTimeout) {
                clearTimeout(submitTimeout);
                submitTimeout = null;
            }

            // 恢复提交状态
            isSubmitting = false;
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;

            console.error('创建订单失败:', error);
            showError('网络错误，请重试');
        });
    }

    // 防抖变量
    let isSubmitting = false;
    let submitTimeout = null;

    // 表单提交前验证和二次确认
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();

        // 防抖检查
        if (isSubmitting) {
            showError('订单正在创建中，请勿重复提交');
            return false;
        }

        if (Object.keys(orderItems).length === 0) {
            showError('请至少选择一道菜品');
            return false;
        }

        // 检查菜单重复
        const dishNames = [];
        const duplicateDishes = [];

        for (const dishId in orderItems) {
            const dishName = orderItems[dishId].name;
            if (dishNames.includes(dishName)) {
                if (!duplicateDishes.includes(dishName)) {
                    duplicateDishes.push(dishName);
                }
            } else {
                dishNames.push(dishName);
            }
        }

        if (duplicateDishes.length > 0) {
            showError('订单中包含重复菜品：' + duplicateDishes.join('、') + '。请检查并修改后重新提交。');
            return false;
        }

        const tableId = document.getElementById('table_id').value;
        if (!tableId) {
            showError('请选择餐桌');
            return false;
        }

        // 显示二次确认对话框
        showOrderConfirmationModal(this);
    });



                // 清空表单
                this.reset();
                orderItems = {};
                updateOrderSummary();

                // 延迟询问是否跳转，给用户时间看到详细信息
                setTimeout(() => {
                    if (confirm('是否跳转到订单管理页面查看详情？')) {
                        window.location.href = '/orders';
                    }
                }, 4000); // 4秒后询问，给Toast足够显示时间
            } else {
                showError(data.message || '创建订单失败');
            }
        })
        .catch(error => {
            // 清除超时保护
            if (submitTimeout) {
                clearTimeout(submitTimeout);
                submitTimeout = null;
            }

            console.error('Error:', error);
            showError('创建订单失败，请重试');
        })
        .finally(() => {
            // 恢复提交状态
            isSubmitting = false;

            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });

        return false;
    });

    // 显示订单创建成功的详细Toast通知
    function showOrderCreatedToast(data) {
        const toastDiv = document.createElement('div');
        toastDiv.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastDiv.style.zIndex = '9999';

        const toastId = 'orderSuccessToast_' + Date.now();
        toastDiv.innerHTML = `
            <div id="${toastId}" class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-success text-white">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <strong class="me-auto">订单创建成功</strong>
                    <small>刚刚</small>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    <div class="d-flex align-items-center mb-2">
                        <i class="bi bi-house-door text-primary me-2"></i>
                        <strong>${data.message}</strong>
                    </div>
                    <div class="small text-muted">
                        <div><i class="bi bi-receipt me-1"></i>订单号：${data.order_number}</div>
                        ${data.total_amount ? `<div><i class="bi bi-currency-yen me-1"></i>金额：¥${data.total_amount.toFixed(2)}</div>` : ''}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(toastDiv);

        // 5秒后自动移除
        setTimeout(() => {
            if (toastDiv.parentNode) {
                toastDiv.remove();
            }
        }, 5000);

        // 添加点击关闭功能
        const closeBtn = toastDiv.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                if (toastDiv.parentNode) {
                    toastDiv.remove();
                }
            });
        }
    }
</script>
<!-- 订单确认模态框 -->
<div class="modal fade" id="orderConfirmationModal" tabindex="-1" aria-labelledby="orderConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderConfirmationModalLabel">
                    <i class="bi bi-check-circle"></i>
                    确认订单信息
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    请仔细核对订单信息，确认无误后点击"确认下单"按钮。
                </div>

                <!-- 包厢信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-house"></i> 包厢信息</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>包厢号：</strong><span id="confirm-table-name"></span>
                            </div>
                            <div class="col-md-6">
                                <strong>包厢类型：</strong><span id="confirm-table-type"></span>
                            </div>
                            <div class="col-md-6">
                                <strong>用餐人数：</strong><span id="confirm-guest-count"></span>人
                            </div>
                            <div class="col-md-6">
                                <strong>用餐时段：</strong><span id="confirm-meal-period"></span>
                            </div>
                        </div>
                        <div class="row mt-2" id="confirm-customer-info" style="display: none;">
                            <div class="col-md-6">
                                <strong>客户姓名：</strong><span id="confirm-customer-name"></span>
                            </div>
                            <div class="col-md-6">
                                <strong>联系电话：</strong><span id="confirm-customer-phone"></span>
                            </div>
                        </div>
                        <div class="mt-2" id="confirm-special-requests" style="display: none;">
                            <strong>特殊要求：</strong><span id="confirm-special-text"></span>
                        </div>
                    </div>
                </div>

                <!-- 菜品列表 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-list"></i> 菜品清单</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>菜品名称</th>
                                        <th>单价</th>
                                        <th>数量</th>
                                        <th>小计</th>
                                    </tr>
                                </thead>
                                <tbody id="confirm-dishes-list">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 费用汇总 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-calculator"></i> 费用汇总</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between">
                                    <span>菜品小计：</span>
                                    <span id="confirm-subtotal" class="fw-bold"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between">
                                    <span>服务费：</span>
                                    <span id="confirm-service-charge" class="fw-bold"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between">
                                    <span>订单总额：</span>
                                    <span id="confirm-total" class="fw-bold text-primary fs-5"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-arrow-left"></i>
                    返回修改
                </button>
                <button type="button" class="btn btn-primary" id="confirm-submit-btn">
                    <i class="bi bi-check-circle"></i>
                    确认下单
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
