<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打荷操作 - 暨阳湖大酒店传菜管理系统</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 10px;
            }

            .card {
                margin-bottom: 15px;
            }

            .btn {
                font-size: 0.9rem;
                padding: 8px 12px;
            }

            .card-header h5 {
                font-size: 1.1rem;
            }
        }

        /* 紧凑布局 */
        .compact-card {
            margin-bottom: 15px;
        }

        .compact-card .card-body {
            padding: 12px;
        }

        /* 大按钮样式 */
        .action-btn {
            min-height: 45px;
            font-weight: bold;
            margin: 2px;
        }

        /* 状态徽章 */
        .status-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }

        /* 菜品卡片优化 */
        .dish-card {
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .dish-card.pending {
            border-left-color: #ffc107;
        }

        .dish-card.cooking {
            border-left-color: #0dcaf0;
        }

        .dish-card.ready {
            border-left-color: #198754;
        }

        .dish-card.completed {
            border-left-color: #6c757d;
            opacity: 0.7;
        }

        .dish-card.completed .dish-name {
            text-decoration: line-through;
        }

        /* 相同菜品标识样式 - 整块背景颜色 */
        .dish-card.cross-room {
            position: relative;
            background: var(--dish-color, #FF6B6B) !important;
            border: 2px solid rgba(0, 0, 0, 0.2);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
        }

        .dish-card.cross-room::before {
            content: '多包厢';
            position: absolute;
            top: -8px;
            right: -8px;
            background: rgba(0, 0, 0, 0.8);
            color: #FFD700;
            font-size: 0.7rem;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 10px;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }

        .dish-card.cross-room .dish-name {
            color: #000 !important;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
        }

        .dish-card.cross-room .card-header {
            background: rgba(0, 0, 0, 0.1) !important;
            color: #000 !important;
        }

        /* 移动端打荷操作页面优化 */
        @media (max-width: 768px) {
            /* 包厢卡片紧凑布局 */
            .room-card {
                margin-bottom: 10px !important;
            }

            .room-card .card-header {
                padding: 10px 12px !important;
                font-size: 16px !important;
                font-weight: 600;
            }

            .room-card .card-body {
                padding: 10px !important;
            }

            /* 菜品卡片移动端优化 */
            .dish-card {
                margin-bottom: 8px !important;
            }

            .dish-card .card-header {
                padding: 8px 10px !important;
                font-size: 15px !important;
            }

            .dish-card .card-body {
                padding: 8px 10px !important;
            }

            /* 操作按钮移动端优化 */
            .action-btn {
                min-height: 44px !important;
                min-width: 44px !important;
                font-size: 14px !important;
                padding: 10px 12px !important;
                margin: 2px !important;
            }

            /* 菜品名称加大 */
            .dish-name {
                font-size: 16px !important;
                font-weight: 600 !important;
                line-height: 1.3 !important;
            }

            /* 包厢信息加大 */
            .room-card .card-header h6 {
                font-size: 17px !important;
                margin-bottom: 0 !important;
            }

            /* 状态标签优化 */
            .badge {
                font-size: 13px !important;
                padding: 4px 8px !important;
            }

            /* 网格布局调整 */
            .col-md-6.col-lg-4.col-xl-3 {
                flex: 0 0 100% !important;
                max-width: 100% !important;
            }

            /* 包厢卡片内的菜品网格 */
            .room-card .row {
                margin: 0 !important;
            }

            .room-card .row > [class*="col-"] {
                padding: 2px !important;
                flex: 0 0 50% !important;
                max-width: 50% !important;
            }

            /* 特殊要求文本 */
            .text-warning small {
                font-size: 13px !important;
            }

            /* 时间显示 */
            .command-time,
            .ready-time {
                font-size: 12px !important;
            }
        }

        /* 平板端优化 */
        @media (min-width: 768px) and (max-width: 1024px) {
            .room-card .card-header {
                font-size: 17px !important;
            }

            .dish-name {
                font-size: 15px !important;
            }

            .action-btn {
                min-height: 42px !important;
                font-size: 14px !important;
            }

            /* 包厢卡片2列布局 */
            .room-card {
                flex: 0 0 50% !important;
                max-width: 50% !important;
            }
        }

        /* 隐藏不必要的信息 */
        .hide-mobile {
            display: block;
        }

        @media (max-width: 768px) {
            .hide-mobile {
                display: none;
            }
        }

        /* 包厢信息显示 */
        .room-info {
            background: #e9ecef;
            border-radius: 6px;
            padding: 8px 12px;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .room-info strong {
            color: #495057;
        }

        /* 打荷操作页面特殊标识 */
        body {
            background-color: #f8f9fa !important;
        }

        .h2 small {
            font-size: 0.6em;
            color: #6c757d !important;
        }

        /* 与厨房大屏区分的样式 */
        .border-bottom {
            border-bottom: 3px solid var(--jiyang-primary) !important;
            background: linear-gradient(135deg, rgba(242, 117, 10, 0.1), rgba(255, 193, 7, 0.1));
            border-radius: 8px 8px 0 0;
        }

        /* 确保导航栏可见 */
        .navbar {
            display: block !important;
            background: linear-gradient(135deg, var(--jiyang-primary), var(--jiyang-secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* 悬浮服务员指令区域样式 */
        .floating-instructions {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            max-width: calc(100vw - 40px);
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 123, 255, 0.3);
            z-index: 1050;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .floating-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-weight: 600;
            cursor: pointer;
            user-select: none;
        }

        .floating-header i {
            margin-right: 8px;
        }

        .btn-close-float {
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .btn-close-float:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .floating-content {
            max-height: 300px;
            overflow-y: auto;
            transition: max-height 0.3s ease;
        }

        .floating-content.collapsed {
            max-height: 0;
        }

        .instruction-item {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: white;
        }

        .instruction-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .instruction-item:last-child {
            border-bottom: none;
        }

        .instruction-main {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .room-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            min-width: 50px;
            text-align: center;
        }

        .waiter-name {
            background: rgba(255, 193, 7, 0.8);
            color: #000;
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
        }

        .action-name {
            font-weight: 600;
            font-size: 14px;
        }

        .instruction-time {
            font-size: 11px;
            opacity: 0.8;
            text-align: right;
        }

        /* 全竖排菜品布局样式 */
        .vertical-dish-container {
            max-height: 70vh;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .room-group {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 0;
        }

        .room-group:last-child {
            border-bottom: none;
        }

        .room-group-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 12px 20px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .room-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .room-name {
            font-weight: 600;
            font-size: 16px;
        }

        .guest-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .dining-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 8px;
        }

        .dining-status.started {
            background: rgba(40, 167, 69, 0.8);
            color: white;
        }

        .dining-status.waiting {
            background: rgba(255, 193, 7, 0.8);
            color: #000;
        }

        .dish-list {
            background: #f8f9fa;
        }

        .dish-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            border-bottom: 1px solid #dee2e6;
            transition: all 0.2s ease;
            position: relative;
            min-height: 70px;
            cursor: pointer;
        }

        .dish-row:hover {
            background: #e9ecef;
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .dish-row:active {
            transform: scale(0.98);
        }

        .dish-row.completed {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }

        .dish-row.cross-room {
            border-left: 4px solid var(--dish-color, #007bff);
            background: linear-gradient(90deg, var(--dish-color, #007bff)10, transparent 10px);
        }

        .dish-info {
            flex: 1;
            min-width: 0;
        }

        .dish-name {
            font-weight: 600;
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
        }

        .dish-name.completed-dish {
            text-decoration: line-through;
            color: #6c757d;
        }

        .special-requests {
            font-size: 12px;
            color: #6c757d;
            font-style: italic;
        }

        .group-indicator {
            font-size: 10px;
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            display: inline-block;
            margin-top: 2px;
        }

        .dish-row.grouped-dish {
            border-left-width: 6px;
        }

        .dish-row.grouped-dish:hover {
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
        }

        /* 快速操作对话框样式 */
        .quick-action-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            animation: fadeIn 0.2s ease;
        }

        .dialog-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
            animation: slideUp 0.3s ease;
        }

        .dialog-header {
            padding: 16px 20px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .dialog-header h6 {
            margin: 0;
            font-weight: 600;
            color: #333;
        }

        .dialog-body {
            padding: 16px 20px;
        }

        .dialog-body p {
            margin: 0;
            color: #666;
        }

        .dialog-actions {
            padding: 0 20px 16px;
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        /* Toast 通知样式 */
        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            z-index: 2100;
            min-width: 300px;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
        }

        .toast-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px 8px;
            border-bottom: 1px solid #dee2e6;
        }

        .toast-header strong {
            color: #333;
        }

        .btn-close-toast {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #999;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toast-body {
            padding: 8px 16px 12px;
            color: #666;
        }

        .toast-info {
            border-left: 4px solid #007bff;
        }

        .toast-warning {
            border-left: 4px solid #ffc107;
        }

        .toast-success {
            border-left: 4px solid #28a745;
        }

        .toast-error {
            border-left: 4px solid #dc3545;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .dish-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-action {
            min-width: 50px;
            min-height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            padding: 0;
        }

        .completed-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .completed-time {
            font-size: 12px;
            color: #28a745;
            font-weight: 600;
        }

        .btn-undo {
            min-width: 40px;
            min-height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }

        /* 左右滑动支持 */
        .dish-row {
            touch-action: pan-y;
            position: relative;
            overflow: hidden;
        }

        .dish-row.swiping {
            transform: translateX(var(--swipe-offset, 0));
            transition: none;
        }

        .dish-row.swipe-action {
            transform: translateX(-80px);
            transition: transform 0.3s ease;
        }

        .swipe-actions {
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 80px;
            background: #dc3545;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .dish-row.swipe-action .swipe-actions {
            opacity: 1;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .floating-instructions {
                width: calc(100vw - 20px);
                right: 10px;
                bottom: 10px;
            }

            .instruction-main {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .room-badge, .waiter-name {
                font-size: 11px;
            }

            .action-name {
                font-size: 13px;
            }

            .dish-row {
                padding: 12px 16px;
                min-height: 60px;
            }

            .dish-name {
                font-size: 15px;
            }

            .btn-action {
                min-width: 44px;
                min-height: 44px;
                font-size: 16px;
            }

            .btn-undo {
                min-width: 36px;
                min-height: 36px;
            }

            .room-group-header {
                padding: 10px 16px;
            }

            .room-name {
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">

    <!-- 弃用通知 -->
    <div class="alert alert-warning alert-dismissible fade show mt-3" role="alert">
        <i class="bi bi-exclamation-triangle-fill"></i>
        <strong>页面已弃用：</strong> 此页面已被新的打荷操作页面替代，建议使用新版本以获得更好的体验。
        <a href="/kitchen-helper" class="btn btn-sm btn-primary ms-2">
            <i class="bi bi-arrow-right"></i> 前往新版打荷操作页面
        </a>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-tools"></i>
        打荷操作中心 <span class="badge bg-warning text-dark">已弃用</span>
        <small class="text-muted">（操作界面 - 保留导航功能）</small>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="/kitchen/display" class="btn btn-sm btn-primary" target="_blank">
                <i class="bi bi-display"></i>
                厨房大屏
            </a>
            <button type="button" class="btn btn-refresh btn-sm" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i>
                刷新
            </button>
        </div>
    </div>
</div>

<!-- 服务员指令通知 - 竖排列表 -->
<div class="row mb-4">
    <div class="col-md-8">
        <!-- 制作任务统计 -->
        <div class="row">
            {% set pending_count = order_items|selectattr("status.value", "equalto", "pending_cook")|list|length %}
            {% set cooking_count = order_items|selectattr("status.value", "equalto", "cooking")|list|length %}
            {% set ready_count = order_items|selectattr("status.value", "equalto", "ready")|list|length %}

            <div class="col-md-4 mb-3">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="card-title h5">待制作</div>
                                <div class="h3">{{ pending_count }}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-clock" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="card-title h5">制作中</div>
                                <div class="h3">{{ cooking_count }}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-arrow-clockwise" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="card-title h5">待上菜</div>
                                <div class="h3">{{ ready_count }}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 服务员指令区域移到悬浮显示 -->
</div>



<!-- 制作任务列表 - 全竖排布局 -->
<div class="card shadow">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">制作任务</h5>
        <div class="task-controls">
            <button class="btn btn-outline-primary btn-sm" onclick="toggleViewMode()">
                <i class="bi bi-grid-3x3-gap" id="viewModeIcon"></i>
                <span id="viewModeText">切换视图</span>
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        {% if orders_by_room %}
        <!-- 全竖排菜品列表容器 -->
        <div class="vertical-dish-container" id="dishContainer">
            {% for room_name, room_data in orders_by_room.items() %}
            {% if room_data is mapping %}
                {% set dish_items = room_data.dish_items %}
                {% set is_served = room_data.is_served %}
                {% set can_operate = room_data.can_operate %}
            {% else %}
                {% set dish_items = room_data %}
                {% set is_served = false %}
                {% set can_operate = true %}
            {% endif %}

            {% if dish_items %}
            <!-- 包厢分组 -->
            <div class="room-group" data-room="{{ room_name }}">
                <div class="room-group-header">
                    <div class="room-info">
                        <span class="room-name">{{ room_name }}</span>
                        <span class="guest-count">{{ room_data.room_info.guest_count or 0 }}人</span>
                        {% if room_data.room_info.dining_start_time %}
                        <span class="dining-status started">{{ room_data.room_info.dining_start_time }}</span>
                        {% else %}
                        <span class="dining-status waiting">未开始</span>
                        {% endif %}
                    </div>
                </div>

                <!-- 菜品列表 -->
                <div class="dish-list">
                    {% for item in dish_items %}
                    {% if item.status.value in ['pending_cook', 'ready'] %}
                    <div class="dish-row {% if item.status.value == 'ready' %}completed{% endif %} {% if item.dish_name in dish_color_map %}cross-room{% endif %}"
                         data-dish-name="{{ item.dish_name }}"
                         data-item-id="{{ item.id }}"
                         data-room="{{ room_name }}"
                         data-status="{{ item.status.value }}"
                         data-dining-started="{{ 'true' if room_data.room_info.dining_start_time or room_data.is_served else 'false' }}"
                         onclick="handleDishRowClick(this)"
                         {% if item.dish_name in dish_color_map %}style="--dish-color: {{ dish_color_map[item.dish_name] }};"{% endif %}>

                        <div class="dish-info">
                            <div class="dish-name {% if item.status.value == 'ready' %}completed-dish{% endif %}">
                                {{ item.dish_name }}
                            </div>
                            {% if item.special_requests %}
                            <div class="special-requests">{{ item.special_requests }}</div>
                            {% endif %}
                        </div>

                        <div class="dish-actions">
                            {% set dining_started = room_data.room_info.dining_start_time or room_data.is_served %}

                            {% if item.status.value == 'pending_cook' and (user.has_permission('dish.cook') or user.has_permission('dish.prepare')) %}
                                {% if dining_started %}
                                <button type="button" class="btn btn-success btn-action"
                                        onclick="updateItemStatus({{ item.id }}, 'ready')"
                                        title="标记完成">
                                    <i class="bi bi-check-circle"></i>
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-secondary btn-action" disabled
                                        title="等待{{ room_name }}包厢开始用餐">
                                    <i class="bi bi-clock"></i>
                                </button>
                                {% endif %}
                            {% endif %}

                            {% if item.status.value == 'ready' %}
                                <div class="completed-info">
                                    <span class="completed-time">{{ item.ready_at.strftime('%H:%M') if item.ready_at else '未知' }}</span>
                                    <button type="button" class="btn btn-outline-warning btn-sm btn-undo"
                                            onclick="undoItemStatus({{ item.id }})"
                                            title="撤销完成">
                                        <i class="bi bi-arrow-counterclockwise"></i>
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
        <!-- 旧的卡片布局已被新的竖排布局替代 -->
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-check-circle text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">暂无制作任务</h4>
            <p class="text-muted">所有菜品都已完成</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 已完成的菜品 -->
<div class="card shadow mt-4">
    <div class="card-header">
        <h5 class="mb-0">今日已完成</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>菜品</th>
                        <th>餐桌</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in order_items %}
                    {% if item.status.value == 'served' %}
                    <tr>
                        <td>{{ item.dish_name }}</td>
                        <td>
                            {% if item.order.table %}{{ item.order.table.number }}{% else %}外带{% endif %}
                        </td>
                        <td><span class="badge bg-success">已完成</span></td>
                    </tr>
                    {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

    </div>

<!-- 悬浮服务员指令区域 -->
{% if waiter_actions %}
<div class="floating-instructions" id="floatingInstructions">
    <div class="floating-header">
        <i class="bi bi-megaphone"></i>
        <span>服务员指令</span>
        <button class="btn-close-float" onclick="toggleFloatingInstructions()">
            <i class="bi bi-chevron-down" id="floatToggleIcon"></i>
        </button>
    </div>
    <div class="floating-content" id="floatingContent">
        {% for action in waiter_actions %}
        {% if not action.is_processed %}
        <div class="instruction-item" onclick="markActionProcessed({{ action.id }})">
            <div class="instruction-main">
                <span class="room-badge">{{ action.room_number }}</span>
                <span class="waiter-name">{{ action.waiter.full_name if action.waiter else '未知' }}</span>
                <span class="action-name">{{ action.action_type_display }}</span>
            </div>
            <div class="instruction-time">{{ action.created_at.strftime('%H:%M') }}</div>
        </div>
        {% endif %}
        {% endfor %}
    </div>
</div>
{% endif %}

<script src="/static/js/bootstrap.bundle.min.js"></script>
<script>
    function updateItemStatus(itemId, status) {
        fetch('/kitchen/items/' + itemId + '/status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: status
            })
        }).then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert('状态更新成功！');

                // 如果是制作完成，播放可配置的语音提示
                if (status === 'ready' && data.voice_message && data.voice_config) {
                    playConfigurableVoice(data.voice_message, data.voice_config, data.broadcast_info);
                }

                setTimeout(() => location.reload(), 1000);
            } else {
                alert('状态更新失败: ' + (data.detail || data.message || '未知错误'));
            }
        }).catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试: ' + error.message);
        });
    }

    // 撤销菜品完成状态
    function undoItemStatus(itemId) {
        if (!confirm('确认撤销此菜品的完成状态？\n\n撤销后菜品将重新变为"待制作"状态。')) {
            return;
        }

        fetch('/kitchen/items/' + itemId + '/undo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert('撤销成功！');
                setTimeout(() => location.reload(), 1000);
            } else {
                alert('撤销失败：' + (data.detail || data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('网络错误，请重试：' + error.message);
        });
    }

    // 可配置的语音播报函数（带播报控制）
    function playConfigurableVoice(message, config, broadcastInfo = {}) {
        if (!config.enabled) {
            console.log('语音播报已禁用');
            return;
        }

        if (!('speechSynthesis' in window)) {
            console.log('浏览器不支持语音播报');
            return;
        }

        // 检查播报控制信息
        if (broadcastInfo.can_broadcast === false) {
            console.log('播报已达到最大次数，跳过播报');
            return;
        }

        let currentRepeat = 0;
        const maxRepeats = broadcastInfo.max_count || config.repeat_count || 2;
        const interval = (broadcastInfo.interval || config.repeat_interval || 3) * 1000; // 转换为毫秒

        function speakOnce() {
            const utterance = new SpeechSynthesisUtterance(message);
            utterance.lang = 'zh-CN';
            utterance.rate = config.rate || 0.8;
            utterance.volume = config.volume || 1.0;
            utterance.pitch = config.pitch || 1.0;

            utterance.onend = function() {
                currentRepeat++;
                console.log(`🔊 语音播报完成 (${currentRepeat}/${maxRepeats}): ${message}`);

                if (currentRepeat < maxRepeats) {
                    setTimeout(speakOnce, interval);
                } else {
                    console.log(`✅ 语音播报全部完成: ${message}`);
                    // 播报完成后，可以在这里记录播报状态
                    recordBroadcastCompletion(message);
                }
            };

            utterance.onerror = function(event) {
                console.error('语音播报错误:', event.error);
                currentRepeat++; // 出错也算一次播报
                if (currentRepeat < maxRepeats) {
                    setTimeout(speakOnce, interval);
                }
            };

            speechSynthesis.speak(utterance);
            console.log(`🔊 开始语音播报 (${currentRepeat + 1}/${maxRepeats}): ${message}`);
        }

        speakOnce();
    }

    // 记录播报完成状态
    function recordBroadcastCompletion(message) {
        // 这里可以发送请求到后端记录播报完成状态
        console.log(`📝 记录播报完成: ${message}`);
    }

    // 退菜功能
    function returnDish(itemId, dishName, tableName) {
        const reason = prompt(`退菜：${dishName} (${tableName})\n\n请输入退菜原因：`, '');

        if (reason === null) {
            return; // 用户取消
        }

        if (!reason.trim()) {
            alert('请输入退菜原因');
            return;
        }

        if (confirm(`确认退菜：${dishName}\n原因：${reason}\n\n此操作将删除该菜品项`)) {
            fetch('/kitchen/items/' + itemId + '/return', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reason: reason.trim()
                })
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('退菜成功：' + data.message);
                    location.reload();
                } else {
                    alert('退菜失败：' + (data.message || '未知错误'));
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('退菜失败，请重试');
            });
        }
    }

    // 标记指令已处理
    function markActionProcessed(actionId) {
        fetch('/kitchen/actions/' + actionId + '/processed', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('标记失败');
            }
        });
    }
    
    // 自动刷新页面
    setInterval(function() {
        location.reload();
    }, 30000); // 每30秒刷新一次

    // 初始化厨房权限检查
    function initKitchenPermissionCheck() {
        // 每30秒检查一次用餐状态，更新按钮状态
        setInterval(checkDiningStatusForAllRooms, 30000);

        // 页面加载时立即检查一次
        setTimeout(checkDiningStatusForAllRooms, 2000);
    }

    // 检查所有包厢的用餐状态
    function checkDiningStatusForAllRooms() {
        const buttons = document.querySelectorAll('button[data-room][data-item-id]');
        const roomsToCheck = new Set();

        buttons.forEach(button => {
            const room = button.getAttribute('data-room');
            if (room && room !== 'unknown') {
                roomsToCheck.add(room);
            }
        });

        roomsToCheck.forEach(room => {
            checkRoomDiningStatus(room);
        });
    }

    // 检查特定包厢的用餐状态
    function checkRoomDiningStatus(roomNumber) {
        fetch(`/api/room-dining-status/${roomNumber}`)
            .then(response => response.json())
            .then(data => {
                updateRoomButtons(roomNumber, data.dining_started);
            })
            .catch(error => {
                console.error(`检查${roomNumber}包厢用餐状态失败:`, error);
            });
    }

    // 更新包厢相关按钮状态
    function updateRoomButtons(roomNumber, diningStarted) {
        const buttons = document.querySelectorAll(`button[data-room="${roomNumber}"]`);

        buttons.forEach(button => {
            const itemId = button.getAttribute('data-item-id');
            const isCompleteButton = button.textContent.includes('制作完成');

            if (isCompleteButton) {
                if (diningStarted) {
                    // 用餐已开始，启用制作完成按钮
                    button.disabled = false;
                    button.classList.remove('btn-secondary');
                    button.classList.add('btn-success');
                    button.title = '';
                    button.innerHTML = '<i class="bi bi-check-circle"></i> 制作完成';
                } else {
                    // 用餐未开始，禁用制作完成按钮
                    button.disabled = true;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-secondary');
                    button.title = `等待${roomNumber}包厢开始用餐`;
                    button.innerHTML = '<i class="bi bi-clock"></i> 等待用餐开始';
                }
            }
        });
    }

    // 悬浮指令区域控制
    function toggleFloatingInstructions() {
        const content = document.getElementById('floatingContent');
        const icon = document.getElementById('floatToggleIcon');

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            icon.className = 'bi bi-chevron-down';
        } else {
            content.classList.add('collapsed');
            icon.className = 'bi bi-chevron-up';
        }
    }

    // 视图模式切换
    let isCompactView = false;
    function toggleViewMode() {
        const container = document.getElementById('dishContainer');
        const icon = document.getElementById('viewModeIcon');
        const text = document.getElementById('viewModeText');

        isCompactView = !isCompactView;

        if (isCompactView) {
            container.classList.add('compact-view');
            icon.className = 'bi bi-list-ul';
            text.textContent = '详细视图';
        } else {
            container.classList.remove('compact-view');
            icon.className = 'bi bi-grid-3x3-gap';
            text.textContent = '紧凑视图';
        }
    }

    // 触摸滑动支持
    class SwipeHandler {
        constructor() {
            this.startX = 0;
            this.startY = 0;
            this.currentX = 0;
            this.currentY = 0;
            this.isDragging = false;
            this.currentElement = null;
            this.threshold = 50; // 滑动阈值

            this.initEventListeners();
        }

        initEventListeners() {
            const container = document.getElementById('dishContainer');
            if (!container) return;

            // 触摸事件
            container.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
            container.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
            container.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });

            // 鼠标事件（用于桌面测试）
            container.addEventListener('mousedown', this.handleMouseDown.bind(this));
            container.addEventListener('mousemove', this.handleMouseMove.bind(this));
            container.addEventListener('mouseup', this.handleMouseUp.bind(this));
        }

        handleTouchStart(e) {
            const dishRow = e.target.closest('.dish-row');
            if (!dishRow) return;

            this.startTouch(e.touches[0], dishRow);
        }

        handleMouseDown(e) {
            const dishRow = e.target.closest('.dish-row');
            if (!dishRow) return;

            this.startTouch(e, dishRow);
        }

        startTouch(touch, element) {
            this.startX = touch.clientX;
            this.startY = touch.clientY;
            this.currentX = touch.clientX;
            this.currentY = touch.clientY;
            this.isDragging = true;
            this.currentElement = element;

            element.classList.add('swiping');
        }

        handleTouchMove(e) {
            if (!this.isDragging || !this.currentElement) return;

            this.updateTouch(e.touches[0]);
            e.preventDefault();
        }

        handleMouseMove(e) {
            if (!this.isDragging || !this.currentElement) return;

            this.updateTouch(e);
        }

        updateTouch(touch) {
            this.currentX = touch.clientX;
            this.currentY = touch.clientY;

            const deltaX = this.currentX - this.startX;
            const deltaY = this.currentY - this.startY;

            // 只处理水平滑动
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                const offset = Math.min(0, deltaX); // 只允许向左滑动
                this.currentElement.style.setProperty('--swipe-offset', `${offset}px`);
            }
        }

        handleTouchEnd(e) {
            this.endTouch();
        }

        handleMouseUp(e) {
            this.endTouch();
        }

        endTouch() {
            if (!this.isDragging || !this.currentElement) return;

            const deltaX = this.currentX - this.startX;

            this.currentElement.classList.remove('swiping');
            this.currentElement.style.removeProperty('--swipe-offset');

            // 判断是否触发滑动操作
            if (deltaX < -this.threshold) {
                this.triggerSwipeAction(this.currentElement);
            }

            this.isDragging = false;
            this.currentElement = null;
        }

        triggerSwipeAction(element) {
            // 这里可以添加滑动后的操作，比如显示更多选项
            console.log('滑动操作触发:', element);

            // 示例：显示删除选项
            element.classList.add('swipe-action');

            // 3秒后自动恢复
            setTimeout(() => {
                element.classList.remove('swipe-action');
            }, 3000);
        }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化厨房权限检查
        initKitchenPermissionCheck();

        // 初始化悬浮指令区域（默认展开）
        const floatingContent = document.getElementById('floatingContent');
        if (floatingContent) {
            // 可以根据需要设置默认状态
            // floatingContent.classList.add('collapsed');
        }

        // 初始化滑动处理器
        new SwipeHandler();

        // 应用相同菜品分组
        applySameDishGrouping();
    });

    // 处理菜品行点击事件
    function handleDishRowClick(element) {
        // 阻止事件冒泡到按钮
        if (event.target.closest('button')) {
            return;
        }

        const itemId = element.getAttribute('data-item-id');
        const status = element.getAttribute('data-status');
        const diningStarted = element.getAttribute('data-dining-started') === 'true';
        const roomName = element.getAttribute('data-room');

        // 添加点击反馈
        element.style.transform = 'scale(0.98)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);

        // 根据当前状态决定操作
        if (status === 'pending_cook') {
            if (diningStarted) {
                // 显示确认对话框
                showQuickActionDialog(itemId, '制作完成', '确认将此菜品标记为制作完成？', () => {
                    updateItemStatus(itemId, 'ready');
                });
            } else {
                showToast('等待用餐开始', `请等待${roomName}包厢开始用餐`, 'warning');
            }
        } else if (status === 'ready') {
            // 显示撤销确认对话框
            showQuickActionDialog(itemId, '撤销完成', '确认撤销此菜品的完成状态？', () => {
                undoItemStatus(itemId);
            });
        }
    }

    // 快速操作对话框
    function showQuickActionDialog(itemId, title, message, callback) {
        const dialog = document.createElement('div');
        dialog.className = 'quick-action-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h6>${title}</h6>
                </div>
                <div class="dialog-body">
                    <p>${message}</p>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-secondary btn-sm" onclick="closeQuickDialog()">取消</button>
                    <button class="btn btn-primary btn-sm" onclick="confirmQuickAction()">确认</button>
                </div>
            </div>
        `;

        // 存储回调函数
        dialog.callback = callback;

        document.body.appendChild(dialog);

        // 添加点击外部关闭
        setTimeout(() => {
            dialog.addEventListener('click', (e) => {
                if (e.target === dialog) {
                    closeQuickDialog();
                }
            });
        }, 100);
    }

    // 关闭快速对话框
    function closeQuickDialog() {
        const dialog = document.querySelector('.quick-action-dialog');
        if (dialog) {
            dialog.remove();
        }
    }

    // 确认快速操作
    function confirmQuickAction() {
        const dialog = document.querySelector('.quick-action-dialog');
        if (dialog && dialog.callback) {
            dialog.callback();
            closeQuickDialog();
        }
    }

    // Toast 通知
    function showToast(title, message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.innerHTML = `
            <div class="toast-header">
                <strong>${title}</strong>
                <button class="btn-close-toast" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="toast-body">${message}</div>
        `;

        document.body.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 3000);
    }

    // 相同菜品分组显示
    function applySameDishGrouping() {
        const dishRows = document.querySelectorAll('.dish-row');
        const dishGroups = {};

        // 按菜品名称分组
        dishRows.forEach(row => {
            const dishName = row.getAttribute('data-dish-name');
            if (!dishGroups[dishName]) {
                dishGroups[dishName] = [];
            }
            dishGroups[dishName].push(row);
        });

        // 为相同菜品添加特殊标识
        Object.keys(dishGroups).forEach(dishName => {
            const rows = dishGroups[dishName];
            if (rows.length > 1) {
                rows.forEach((row, index) => {
                    row.classList.add('grouped-dish');
                    row.setAttribute('data-group-size', rows.length);
                    row.setAttribute('data-group-index', index + 1);

                    // 添加分组指示器
                    const dishInfo = row.querySelector('.dish-info');
                    if (dishInfo && !dishInfo.querySelector('.group-indicator')) {
                        const indicator = document.createElement('div');
                        indicator.className = 'group-indicator';
                        indicator.textContent = `${index + 1}/${rows.length}`;
                        dishInfo.appendChild(indicator);
                    }
                });
            }
        });
    }
</script>
</body>
</html>
