#!/usr/bin/env python3
"""
暨阳湖大酒店传菜管理系统 - 完整系统重置脚本
清理所有订单数据、重置包厢状态、清除服务员授权
"""

import os
import sys
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_db_path():
    """获取数据库路径"""
    return os.path.join(os.path.dirname(__file__), 'paocai.db')

def complete_system_reset():
    """完整系统重置"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 开始完整系统重置...")
        print("=" * 50)
        
        # 1. 清空订单项数据
        cursor.execute("DELETE FROM order_items")
        deleted_items = cursor.rowcount
        print(f"✅ 清理订单项数据: {deleted_items} 条")
        
        # 2. 清空订单数据
        cursor.execute("DELETE FROM orders")
        deleted_orders = cursor.rowcount
        print(f"✅ 清理订单数据: {deleted_orders} 条")
        
        # 3. 清空服务员操作记录
        cursor.execute("DELETE FROM waiter_actions")
        deleted_actions = cursor.rowcount
        print(f"✅ 清理服务员操作记录: {deleted_actions} 条")
        
        # 4. 重置所有包厢状态
        cursor.execute("""
            UPDATE tables 
            SET status = 'available',
                current_guests = 0,
                assigned_waiter_id = NULL,
                estimated_duration = NULL
        """)
        updated_tables = cursor.rowcount
        print(f"✅ 重置包厢状态: {updated_tables} 个包厢")
        
        # 5. 清除所有服务员授权状态
        cursor.execute("""
            UPDATE users
            SET assigned_tables = NULL,
                is_authorized = 0,
                authorized_by = NULL
            WHERE role IN ('waiter', 'kitchen_helper')
        """)
        updated_users = cursor.rowcount
        print(f"✅ 清除服务员授权: {updated_users} 个用户")
        
        # 6. 重置自增ID（可选）
        cursor.execute("DELETE FROM sqlite_sequence WHERE name IN ('orders', 'order_items', 'waiter_actions')")
        print("✅ 重置自增ID序列")
        
        # 提交所有更改
        conn.commit()
        
        print("=" * 50)
        print("🎉 完整系统重置完成！")
        print(f"📊 清理统计:")
        print(f"   - 订单项: {deleted_items} 条")
        print(f"   - 订单: {deleted_orders} 条")
        print(f"   - 操作记录: {deleted_actions} 条")
        print(f"   - 包厢重置: {updated_tables} 个")
        print(f"   - 用户授权清除: {updated_users} 个")
        
        # 验证数据一致性
        verify_data_consistency(cursor)
        
        return True
        
    except Exception as e:
        print(f"❌ 系统重置失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def verify_data_consistency(cursor):
    """验证数据一致性"""
    print("\n🔍 验证数据一致性...")
    
    # 检查订单数量
    cursor.execute("SELECT COUNT(*) FROM orders")
    order_count = cursor.fetchone()[0]
    
    # 检查订单项数量
    cursor.execute("SELECT COUNT(*) FROM order_items")
    item_count = cursor.fetchone()[0]
    
    # 检查可用包厢数量
    cursor.execute("SELECT COUNT(*) FROM tables WHERE status = 'available'")
    available_tables = cursor.fetchone()[0]
    
    # 检查授权服务员数量
    cursor.execute("SELECT COUNT(*) FROM users WHERE is_authorized = 1 AND role IN ('waiter', 'kitchen_helper')")
    authorized_users = cursor.fetchone()[0]
    
    print(f"📋 数据一致性检查:")
    print(f"   - 剩余订单: {order_count} 个")
    print(f"   - 剩余订单项: {item_count} 个")
    print(f"   - 可用包厢: {available_tables} 个")
    print(f"   - 授权用户: {authorized_users} 个")
    
    if order_count == 0 and item_count == 0 and authorized_users == 0:
        print("✅ 数据一致性验证通过")
        return True
    else:
        print("⚠️ 数据一致性验证发现问题")
        return False

def backup_database():
    """备份数据库"""
    db_path = get_db_path()
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"💾 数据库已备份到: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ 数据库备份失败: {e}")
        return False

def main():
    """主函数"""
    print("🏨 暨阳湖大酒店传菜管理系统 - 完整系统重置")
    print("⚠️  警告: 此操作将清除所有订单数据和服务员授权状态")
    
    # 确认操作
    confirm = input("\n确定要执行完整系统重置吗？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("❌ 操作已取消")
        return
    
    # 备份数据库
    print("\n📦 正在备份数据库...")
    if not backup_database():
        print("❌ 备份失败，操作终止")
        return
    
    # 执行重置
    if complete_system_reset():
        print("\n🚀 系统重置成功！可以重新启动服务程序。")
    else:
        print("\n❌ 系统重置失败！请检查错误信息。")

if __name__ == "__main__":
    main()
