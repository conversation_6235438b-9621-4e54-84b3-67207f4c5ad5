#!/usr/bin/env python3
"""
数据库初始化脚本
创建数据库表并插入示例数据
"""

from sqlalchemy.orm import Session
from core.database import engine, Base, SessionLocal
from core.security import get_password_hash
from models.user import User, UserRole, UserStatus
from models.table import Table, TableType, TableStatus
from models.menu import Dish, DishCategory, DishStatus, SpicyLevel, CookingMethod
from models.order import Order, OrderItem, OrderStatus, OrderType, DishItemStatus
from models.waiter_action import WaiterAction
from datetime import datetime
import uuid


def create_tables():
    """创建数据库表"""
    print("创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表创建完成")


def create_sample_users(db: Session):
    """创建示例用户"""
    print("创建示例用户...")
    
    users_data = [
        {
            "username": "admin",
            "password": "admin123",
            "full_name": "系统管理员",
            "role": UserRole.ADMIN,
            "email": "<EMAIL>",
            "employee_id": "EMP001",
            "department": "管理部",
            "position": "系统管理员"
        },
        {
            "username": "manager01",
            "password": "manager123",
            "full_name": "张经理",
            "role": UserRole.MANAGER,
            "email": "<EMAIL>",
            "phone": "13800138001",
            "employee_id": "EMP002",
            "department": "餐饮部",
            "position": "餐饮经理"
        },
        {
            "username": "waiter01",
            "password": "waiter123",
            "full_name": "李小美",
            "role": UserRole.WAITER,
            "phone": "13800138002",
            "employee_id": "EMP003",
            "department": "服务部",
            "position": "服务员"
        },
        {
            "username": "waiter02",
            "password": "waiter123",
            "full_name": "王小丽",
            "role": UserRole.WAITER,
            "phone": "***********",
            "employee_id": "EMP004",
            "department": "服务部",
            "position": "服务员"
        },
        {
            "username": "kitchen01",
            "password": "kitchen123",
            "full_name": "陈师傅",
            "role": UserRole.KITCHEN_HELPER,
            "phone": "***********",
            "employee_id": "EMP005",
            "department": "厨房部",
            "position": "厨房打荷"
        },
        {
            "username": "business01",
            "password": "business123",
            "full_name": "李商务",
            "role": UserRole.BUSINESS_CENTER,
            "phone": "***********",
            "employee_id": "EMP006",
            "department": "商务部",
            "position": "商务中心"
        },
        {
            "username": "kitchen02",
            "password": "kitchen123",
            "full_name": "小张",
            "role": UserRole.KITCHEN_HELPER,
            "phone": "***********",
            "employee_id": "EMP007",
            "department": "厨房部",
            "position": "打荷员"
        },

    ]
    
    for user_data in users_data:
        # 检查用户是否已存在
        existing_user = db.query(User).filter(User.username == user_data["username"]).first()
        if existing_user:
            print(f"用户 {user_data['username']} 已存在，跳过创建")
            continue
        
        # 创建新用户
        user = User(
            username=user_data["username"],
            hashed_password=get_password_hash(user_data["password"]),
            full_name=user_data["full_name"],
            role=user_data["role"],
            status=UserStatus.ACTIVE,
            email=user_data.get("email"),
            phone=user_data.get("phone"),
            employee_id=user_data.get("employee_id"),
            department=user_data.get("department"),
            position=user_data.get("position"),
            is_active=True,
            created_at=datetime.utcnow()
        )
        db.add(user)
        print(f"✅ 创建用户: {user_data['full_name']} ({user_data['username']})")
    
    db.commit()


def create_sample_tables(db: Session):
    """创建示例餐桌"""
    print("创建示例餐桌...")
    
    tables_data = [
        # 大厅餐桌
        {"number": "A01", "name": "大厅1号桌", "table_type": TableType.HALL_TABLE, "capacity": 4, "floor": "1F", "area": "大厅A区"},
        {"number": "A02", "name": "大厅2号桌", "table_type": TableType.HALL_TABLE, "capacity": 4, "floor": "1F", "area": "大厅A区"},
        {"number": "A03", "name": "大厅3号桌", "table_type": TableType.HALL_TABLE, "capacity": 6, "floor": "1F", "area": "大厅A区"},
        {"number": "A04", "name": "大厅4号桌", "table_type": TableType.HALL_TABLE, "capacity": 6, "floor": "1F", "area": "大厅A区"},
        {"number": "A05", "name": "大厅5号桌", "table_type": TableType.HALL_TABLE, "capacity": 8, "floor": "1F", "area": "大厅A区"},
        
        {"number": "B01", "name": "大厅6号桌", "table_type": TableType.HALL_TABLE, "capacity": 4, "floor": "1F", "area": "大厅B区"},
        {"number": "B02", "name": "大厅7号桌", "table_type": TableType.HALL_TABLE, "capacity": 4, "floor": "1F", "area": "大厅B区"},
        {"number": "B03", "name": "大厅8号桌", "table_type": TableType.HALL_TABLE, "capacity": 6, "floor": "1F", "area": "大厅B区"},
        {"number": "B04", "name": "大厅9号桌", "table_type": TableType.HALL_TABLE, "capacity": 6, "floor": "1F", "area": "大厅B区"},
        {"number": "B05", "name": "大厅10号桌", "table_type": TableType.HALL_TABLE, "capacity": 8, "floor": "1F", "area": "大厅B区"},
        
        # 包厢
        {"number": "VIP01", "name": "暨阳厅", "table_type": TableType.VIP_ROOM, "capacity": 12, "floor": "2F", "area": "VIP区", "minimum_charge": 1000, "has_tv": True, "has_karaoke": True},
        {"number": "VIP02", "name": "湖景厅", "table_type": TableType.VIP_ROOM, "capacity": 16, "floor": "2F", "area": "VIP区", "minimum_charge": 1500, "has_tv": True, "has_karaoke": True, "has_projector": True},
        {"number": "VIP03", "name": "雅致厅", "table_type": TableType.VIP_ROOM, "capacity": 20, "floor": "2F", "area": "VIP区", "minimum_charge": 2000, "has_tv": True, "has_karaoke": True, "has_mahjong": True},
        
        {"number": "P01", "name": "包厢1", "table_type": TableType.PRIVATE_ROOM, "capacity": 8, "floor": "2F", "area": "包厢区", "minimum_charge": 500, "has_tv": True},
        {"number": "P02", "name": "包厢2", "table_type": TableType.PRIVATE_ROOM, "capacity": 10, "floor": "2F", "area": "包厢区", "minimum_charge": 600, "has_tv": True},
    ]
    
    for table_data in tables_data:
        # 检查餐桌是否已存在
        existing_table = db.query(Table).filter(Table.number == table_data["number"]).first()
        if existing_table:
            print(f"餐桌 {table_data['number']} 已存在，跳过创建")
            continue
        
        # 创建新餐桌
        table = Table(
            number=table_data["number"],
            name=table_data.get("name"),
            table_type=table_data["table_type"],
            status=TableStatus.AVAILABLE,
            capacity=table_data["capacity"],
            floor=table_data.get("floor"),
            area=table_data.get("area"),
            minimum_charge=table_data.get("minimum_charge"),
            has_tv=table_data.get("has_tv", False),
            has_karaoke=table_data.get("has_karaoke", False),
            has_mahjong=table_data.get("has_mahjong", False),
            has_projector=table_data.get("has_projector", False),
            has_wifi=True,
            has_air_conditioning=True,
            is_active=True,
            created_at=datetime.utcnow()
        )
        db.add(table)
        print(f"✅ 创建餐桌: {table_data['number']} - {table_data.get('name', '')}")
    
    db.commit()


def create_sample_dishes(db: Session):
    """创建示例菜品"""
    print("创建示例菜品...")
    
    dishes_data = [
        # 招牌菜
        {
            "name": "暨阳湖大闸蟹", "category": DishCategory.SEAFOOD, "price": 88.00, "cost": 45.00,
            "description": "正宗阳澄湖大闸蟹，肉质鲜美，蟹黄丰满", "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.STEAM, "total_time": 25, "is_signature": True, "is_recommended": True
        },
        {
            "name": "红烧狮子头", "category": DishCategory.HOT_DISH, "price": 48.00, "cost": 18.00,
            "description": "传统淮扬菜，肉质鲜嫩，汤汁浓郁", "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.BRAISE, "total_time": 35, "is_signature": True
        },
        {
            "name": "清蒸鲈鱼", "category": DishCategory.SEAFOOD, "price": 68.00, "cost": 35.00,
            "description": "新鲜鲈鱼，肉质细嫩，营养丰富", "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.STEAM, "total_time": 20, "is_recommended": True
        },
        
        # 热菜
        {
            "name": "宫保鸡丁", "category": DishCategory.HOT_DISH, "price": 32.00, "cost": 12.00,
            "description": "经典川菜，鸡肉嫩滑，花生香脆", "spicy_level": SpicyLevel.MEDIUM,
            "cooking_method": CookingMethod.STIR_FRY, "total_time": 15, "is_popular": True
        },
        {
            "name": "糖醋里脊", "category": DishCategory.HOT_DISH, "price": 36.00, "cost": 15.00,
            "description": "酸甜可口，外酥内嫩", "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.DEEP_FRY, "total_time": 20
        },
        {
            "name": "麻婆豆腐", "category": DishCategory.HOT_DISH, "price": 22.00, "cost": 8.00,
            "description": "经典川菜，麻辣鲜香", "spicy_level": SpicyLevel.HOT,
            "cooking_method": CookingMethod.STIR_FRY, "total_time": 12, "is_popular": True
        },
        {
            "name": "回锅肉", "category": DishCategory.HOT_DISH, "price": 38.00, "cost": 16.00,
            "description": "四川名菜，肥而不腻", "spicy_level": SpicyLevel.MEDIUM,
            "cooking_method": CookingMethod.STIR_FRY, "total_time": 18
        },
        
        # 凉菜
        {
            "name": "口水鸡", "category": DishCategory.COLD_DISH, "price": 28.00, "cost": 12.00,
            "description": "四川凉菜，麻辣鲜香", "spicy_level": SpicyLevel.HOT,
            "cooking_method": CookingMethod.COLD_MIX, "total_time": 30
        },
        {
            "name": "蒜泥白肉", "category": DishCategory.COLD_DISH, "price": 32.00, "cost": 15.00,
            "description": "川菜经典，肉片薄如纸", "spicy_level": SpicyLevel.MILD,
            "cooking_method": CookingMethod.BOIL, "total_time": 25
        },
        {
            "name": "凉拌黄瓜", "category": DishCategory.COLD_DISH, "price": 12.00, "cost": 4.00,
            "description": "清爽开胃，简单美味", "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.COLD_MIX, "total_time": 10
        },
        
        # 汤羹
        {
            "name": "西湖牛肉羹", "category": DishCategory.SOUP, "price": 25.00, "cost": 10.00,
            "description": "杭州名菜，汤鲜味美", "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.BOIL, "total_time": 20
        },
        {
            "name": "酸辣汤", "category": DishCategory.SOUP, "price": 18.00, "cost": 6.00,
            "description": "酸辣开胃，营养丰富", "spicy_level": SpicyLevel.MEDIUM,
            "cooking_method": CookingMethod.BOIL, "total_time": 15
        },
        {
            "name": "冬瓜排骨汤", "category": DishCategory.SOUP, "price": 28.00, "cost": 12.00,
            "description": "清淡营养，老少皆宜", "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.STEW, "total_time": 45
        },
        
        # 主食
        {
            "name": "扬州炒饭", "category": DishCategory.STAPLE, "price": 18.00, "cost": 6.00,
            "description": "经典炒饭，粒粒分明", "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.STIR_FRY, "total_time": 10, "is_popular": True
        },
        {
            "name": "牛肉面", "category": DishCategory.STAPLE, "price": 22.00, "cost": 8.00,
            "description": "汤浓肉烂，面条劲道", "spicy_level": SpicyLevel.MILD,
            "cooking_method": CookingMethod.BOIL, "total_time": 15
        },
        {
            "name": "小笼包", "category": DishCategory.STAPLE, "price": 16.00, "cost": 5.00,
            "description": "皮薄馅大，汤汁鲜美", "spicy_level": SpicyLevel.NONE,
            "cooking_method": CookingMethod.STEAM, "total_time": 20, "unit": "笼"
        },
        
        # 饮品
        {
            "name": "鲜榨橙汁", "category": DishCategory.BEVERAGE, "price": 15.00, "cost": 5.00,
            "description": "新鲜橙子现榨，维C丰富", "spicy_level": SpicyLevel.NONE,
            "total_time": 5, "unit": "杯"
        },
        {
            "name": "柠檬蜂蜜茶", "category": DishCategory.BEVERAGE, "price": 12.00, "cost": 4.00,
            "description": "清香甘甜，美容养颜", "spicy_level": SpicyLevel.NONE,
            "total_time": 3, "unit": "杯"
        },
        {
            "name": "绿豆汤", "category": DishCategory.BEVERAGE, "price": 8.00, "cost": 2.00,
            "description": "清热解毒，消暑佳品", "spicy_level": SpicyLevel.NONE,
            "total_time": 5, "unit": "碗"
        },
    ]
    
    for dish_data in dishes_data:
        # 检查菜品是否已存在
        existing_dish = db.query(Dish).filter(Dish.name == dish_data["name"]).first()
        if existing_dish:
            print(f"菜品 {dish_data['name']} 已存在，跳过创建")
            continue
        
        # 计算利润率
        profit_margin = 0
        if dish_data.get("cost") and dish_data["price"] > 0:
            profit_margin = round(((dish_data["price"] - dish_data["cost"]) / dish_data["price"]) * 100)
        
        # 创建新菜品
        dish = Dish(
            name=dish_data["name"],
            category=dish_data["category"],
            status=DishStatus.AVAILABLE,
            description=dish_data.get("description"),
            price=dish_data["price"],
            cost=dish_data.get("cost"),
            unit=dish_data.get("unit", "份"),
            spicy_level=dish_data.get("spicy_level", SpicyLevel.NONE),
            cooking_method=dish_data.get("cooking_method"),
            prep_time=dish_data.get("prep_time", 0),
            cook_time=dish_data.get("cook_time", dish_data.get("total_time", 0)),
            is_signature=dish_data.get("is_signature", False),
            is_recommended=dish_data.get("is_recommended", False),
            is_new=dish_data.get("is_new", False),
            is_popular=dish_data.get("is_popular", False),
            min_order_quantity=1,
            is_active=True,
            created_at=datetime.utcnow()
        )
        db.add(dish)
        print(f"✅ 创建菜品: {dish_data['name']} - ¥{dish_data['price']}")
    
    db.commit()


def main():
    """主函数"""
    print("=" * 50)
    print("暨阳湖大酒店传菜管理系统 - 数据库初始化")
    print("=" * 50)
    
    try:
        # 创建数据库表
        create_tables()
        
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            # 创建示例数据
            create_sample_users(db)
            create_sample_tables(db)
            create_sample_dishes(db)
            
            print("\n" + "=" * 50)
            print("✅ 数据库初始化完成！")
            print("=" * 50)
            print("\n默认登录账号:")
            print("管理员: admin / admin123")
            print("经理: manager01 / manager123")
            print("服务员: waiter01 / waiter123")
            print("厨房打荷: kitchen01 / kitchen123")
            print("商务中心: business01 / business123")
            print("\n现在可以启动系统了:")
            print("python main.py")
            print("=" * 50)
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise


if __name__ == "__main__":
    main()
