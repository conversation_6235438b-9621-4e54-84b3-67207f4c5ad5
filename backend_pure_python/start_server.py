#!/usr/bin/env python3
"""
简单的服务器启动脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔍 检查依赖...")
    import fastapi
    print("✅ FastAPI 已安装")
    
    import uvicorn
    print("✅ Uvicorn 已安装")
    
    import sqlalchemy
    print("✅ SQLAlchemy 已安装")
    
    print("🚀 启动服务器...")
    
    # 导入主应用
    from main import app
    print("✅ 应用导入成功")
    
    # 启动服务器
    uvicorn.run(app, host="0.0.0.0", port=8001, reload=False)
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请检查虚拟环境是否正确激活")
    sys.exit(1)
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
