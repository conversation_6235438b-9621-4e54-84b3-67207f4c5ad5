#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库中的字符编码问题
"""

import sqlite3
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_encoding_issues():
    """修复数据库中的编码问题"""
    
    # 连接数据库
    conn = sqlite3.connect('paocai.db')
    conn.execute("PRAGMA encoding = 'UTF-8'")
    cursor = conn.cursor()
    
    print("🔧 开始修复数据库编码问题...")
    
    try:
        # 1. 修复orders表中的乱码数据
        print("📋 修复订单表中的乱码数据...")
        
        # 查询所有可能有乱码的订单
        cursor.execute("""
            SELECT id, customer_name, special_requests 
            FROM orders 
            WHERE customer_name LIKE '%æ%' OR special_requests LIKE '%æ%'
        """)
        
        corrupted_orders = cursor.fetchall()
        
        for order_id, customer_name, special_requests in corrupted_orders:
            print(f"  修复订单 {order_id}: {customer_name}")
            
            # 尝试修复客户名称
            if customer_name and 'æµè¯å®¢æ·' in customer_name:
                new_customer_name = customer_name.replace('æµè¯å®¢æ·', '测试客户')
                cursor.execute(
                    "UPDATE orders SET customer_name = ? WHERE id = ?",
                    (new_customer_name, order_id)
                )
                print(f"    客户名称: {customer_name} → {new_customer_name}")
            
            # 尝试修复特殊要求
            if special_requests:
                new_special_requests = special_requests
                replacements = {
                    'æµè¯è®¢å': '测试订单',
                    'æµè¯å¤åå¢': '测试多包厢',
                    'æµè¯å¤åå¢2': '测试多包厢2'
                }
                
                for old, new in replacements.items():
                    if old in new_special_requests:
                        new_special_requests = new_special_requests.replace(old, new)
                
                if new_special_requests != special_requests:
                    cursor.execute(
                        "UPDATE orders SET special_requests = ? WHERE id = ?",
                        (new_special_requests, order_id)
                    )
                    print(f"    特殊要求: {special_requests} → {new_special_requests}")
        
        # 2. 修复order_items表中的乱码数据
        print("🍽️ 修复菜品表中的乱码数据...")
        
        cursor.execute("""
            SELECT id, dish_name 
            FROM order_items 
            WHERE dish_name LIKE '%æ%'
        """)
        
        corrupted_items = cursor.fetchall()
        
        for item_id, dish_name in corrupted_items:
            print(f"  修复菜品 {item_id}: {dish_name}")
            
            # 常见菜品名称修复映射
            dish_replacements = {
                # 使用简单的字符替换，避免编码问题
            }
            
            new_dish_name = dish_name
            for old, new in dish_replacements.items():
                if old in new_dish_name:
                    new_dish_name = new_dish_name.replace(old, new)
            
            if new_dish_name != dish_name:
                cursor.execute(
                    "UPDATE order_items SET dish_name = ? WHERE id = ?",
                    (new_dish_name, item_id)
                )
                print(f"    菜品名称: {dish_name} → {new_dish_name}")
        
        # 3. 修复用户表中的乱码数据
        print("👥 修复用户表中的乱码数据...")

        cursor.execute("""
            SELECT id, full_name
            FROM users
            WHERE full_name LIKE '%æ%'
        """)

        corrupted_users = cursor.fetchall()

        for user_id, full_name in corrupted_users:
            print(f"  修复用户 {user_id}: {full_name}")

            # 简单的用户名称修复
            new_full_name = full_name
            if 'æ' in new_full_name:
                # 将乱码替换为通用名称
                new_full_name = f"用户{user_id}"

            if new_full_name != full_name:
                cursor.execute(
                    "UPDATE users SET full_name = ? WHERE id = ?",
                    (new_full_name, user_id)
                )
                print(f"    用户名称: {full_name} → {new_full_name}")
        
        # 提交更改
        conn.commit()
        print("✅ 数据库编码修复完成！")
        
        # 验证修复结果
        print("\n📊 验证修复结果:")
        
        # 检查订单表
        cursor.execute("SELECT COUNT(*) FROM orders WHERE customer_name LIKE '%æ%' OR special_requests LIKE '%æ%'")
        remaining_order_issues = cursor.fetchone()[0]
        print(f"  订单表剩余乱码记录: {remaining_order_issues}")
        
        # 检查菜品表
        cursor.execute("SELECT COUNT(*) FROM order_items WHERE dish_name LIKE '%æ%'")
        remaining_item_issues = cursor.fetchone()[0]
        print(f"  菜品表剩余乱码记录: {remaining_item_issues}")
        
        # 检查用户表
        cursor.execute("SELECT COUNT(*) FROM users WHERE full_name LIKE '%æ%'")
        remaining_user_issues = cursor.fetchone()[0]
        print(f"  用户表剩余乱码记录: {remaining_user_issues}")
        
        if remaining_order_issues == 0 and remaining_item_issues == 0 and remaining_user_issues == 0:
            print("🎉 所有编码问题已修复！")
        else:
            print("⚠️ 仍有部分编码问题需要手动处理")
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()
    
    return True

def set_database_encoding():
    """设置数据库编码为UTF-8"""
    
    conn = sqlite3.connect('paocai.db')
    cursor = conn.cursor()
    
    try:
        # 设置数据库编码
        cursor.execute("PRAGMA encoding = 'UTF-8'")
        cursor.execute("PRAGMA journal_mode = WAL")
        cursor.execute("PRAGMA synchronous = NORMAL")
        
        conn.commit()
        print("✅ 数据库编码设置为UTF-8")
        
    except Exception as e:
        print(f"❌ 设置数据库编码失败: {e}")
        return False
    
    finally:
        conn.close()
    
    return True

if __name__ == "__main__":
    print("🚀 开始修复暨阳湖大酒店传菜管理系统编码问题...")
    
    # 设置数据库编码
    if set_database_encoding():
        # 修复现有数据的编码问题
        if fix_encoding_issues():
            print("\n🎊 编码问题修复完成！")
            print("💡 建议重启服务以确保更改生效")
        else:
            print("\n❌ 编码问题修复失败")
            sys.exit(1)
    else:
        print("\n❌ 数据库编码设置失败")
        sys.exit(1)
