#!/usr/bin/env python3
"""
测试API访问权限
"""
import requests
import json

def test_api_access():
    """测试API访问权限"""
    base_url = "http://localhost:8001"
    
    # 测试用户登录 - 使用数据库中实际存在的服务员账号
    login_data = {
        "username": "1",  # 数据库中的服务员用户名
        "password": "123456"  # 尝试默认密码
    }
    
    print("🔍 测试服务员登录...")
    session = requests.Session()
    
    # 登录
    login_response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
    if login_response.status_code == 302:
        print("✅ 服务员登录成功")
        # 获取重定向后的页面来确保session有效
        redirect_response = session.get(f"{base_url}/waiter/menu")
        if redirect_response.status_code == 200:
            print("✅ 服务员界面访问成功")
        else:
            print(f"❌ 服务员界面访问失败: {redirect_response.status_code}")
    else:
        print(f"❌ 服务员登录失败: {login_response.status_code}")
        print(f"响应内容: {login_response.text}")
        return
    
    # 测试系统配置API
    print("\n🔍 测试系统配置API访问...")
    config_response = session.get(f"{base_url}/api/system-config")
    print(f"状态码: {config_response.status_code}")
    
    if config_response.status_code == 200:
        print("✅ 系统配置API访问成功")
        config_data = config_response.json()
        print(f"获取到的配置: {json.dumps(config_data, indent=2, ensure_ascii=False)}")
    elif config_response.status_code == 403:
        print("❌ 系统配置API访问被拒绝 (403 Forbidden)")
        print(f"响应内容: {config_response.text}")
    else:
        print(f"❌ 系统配置API访问失败: {config_response.status_code}")
        print(f"响应内容: {config_response.text}")
    
    # 测试用餐状态检查API
    print("\n🔍 测试用餐状态检查API...")
    dining_status_response = session.get(f"{base_url}/waiter/check-dining-status/test1")
    print(f"状态码: {dining_status_response.status_code}")
    
    if dining_status_response.status_code == 200:
        print("✅ 用餐状态检查API访问成功")
        status_data = dining_status_response.json()
        print(f"用餐状态: {json.dumps(status_data, indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ 用餐状态检查API访问失败: {config_response.status_code}")
        print(f"响应内容: {dining_status_response.text}")
    
    # 测试指令模板API
    print("\n🔍 测试指令模板API...")
    templates_response = session.get(f"{base_url}/api/command-templates")
    print(f"状态码: {templates_response.status_code}")
    
    if templates_response.status_code == 200:
        print("✅ 指令模板API访问成功")
        templates_data = templates_response.json()
        print(f"指令模板数量: {len(templates_data)}")
    else:
        print(f"❌ 指令模板API访问失败: {templates_response.status_code}")

if __name__ == "__main__":
    test_api_access()
