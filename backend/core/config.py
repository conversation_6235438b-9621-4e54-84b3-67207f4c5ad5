from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "暨阳湖大酒店传菜管理系统"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://postgres:password@localhost:5432/paocai_db"
    
    # JWT 配置
    SECRET_KEY: str = "jiyang-lake-hotel-secret-key-2024"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30 * 24 * 60  # 30天
    
    # CORS 配置
    ALLOWED_HOSTS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000"
    ]
    
    # Redis 配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 文件上传配置
    UPLOAD_DIR: str = "static/uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # 语音播报配置
    TTS_ENABLED: bool = True
    TTS_LANGUAGE: str = "zh-cn"
    
    class Config:
        env_file = ".env"


settings = Settings()
