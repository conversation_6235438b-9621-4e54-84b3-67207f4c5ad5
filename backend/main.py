from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import socketio
from contextlib import asynccontextmanager

from core.config import settings
from core.database import engine, Base
from api import auth, menu, order, table, user
from core.websocket import sio


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时创建数据库表
    Base.metadata.create_all(bind=engine)
    yield
    # 关闭时的清理工作


# 创建 FastAPI 应用
app = FastAPI(
    title="暨阳湖大酒店传菜管理系统",
    description="现代化的餐厅管理系统，提供完整的服务流程管理",
    version="1.0.0",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 集成 Socket.IO
socket_app = socketio.ASGIApp(sio, app)

# 注册路由
app.include_router(auth.router, prefix="/api/auth", tags=["认证"])
app.include_router(user.router, prefix="/api/users", tags=["用户管理"])
app.include_router(menu.router, prefix="/api/menu", tags=["菜单管理"])
app.include_router(table.router, prefix="/api/tables", tags=["餐桌管理"])
app.include_router(order.router, prefix="/api/orders", tags=["订单管理"])


@app.get("/")
async def root():
    return {
        "message": "欢迎使用暨阳湖大酒店传菜管理系统",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:socket_app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
