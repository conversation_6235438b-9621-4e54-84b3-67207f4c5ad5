from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session
from sqlalchemy import or_
from pydantic import BaseModel
from decimal import Decimal
import os
import uuid
from pathlib import Path

from core.database import get_db
from core.config import settings
from models.menu import Dish, DishCategory, DishStatus, SpicyLevel, CookingMethod
from models.user import User
from .auth import get_current_user, require_permission

router = APIRouter()


# Pydantic 模型
class DishCreate(BaseModel):
    name: str
    english_name: Optional[str] = None
    code: Optional[str] = None
    category: DishCategory
    description: Optional[str] = None
    ingredients: Optional[str] = None
    allergens: Optional[str] = None
    price: Decimal
    cost: Optional[Decimal] = None
    market_price: Optional[Decimal] = None
    member_price: Optional[Decimal] = None
    unit: str = "份"
    serving_size: Optional[str] = None
    weight: Optional[Decimal] = None
    spicy_level: SpicyLevel = SpicyLevel.NONE
    cooking_method: Optional[CookingMethod] = None
    taste_tags: Optional[str] = None
    prep_time: Optional[int] = None
    cook_time: Optional[int] = None
    difficulty: Optional[int] = None
    kitchen_station: Optional[str] = None
    calories: Optional[int] = None
    protein: Optional[Decimal] = None
    fat: Optional[Decimal] = None
    carbs: Optional[Decimal] = None
    is_recommended: bool = False
    is_signature: bool = False
    is_new: bool = False
    daily_limit: Optional[int] = None
    min_order_quantity: int = 1
    max_order_quantity: Optional[int] = None
    available_start_time: Optional[str] = None
    available_end_time: Optional[str] = None
    sort_order: int = 0


class DishUpdate(BaseModel):
    name: Optional[str] = None
    english_name: Optional[str] = None
    code: Optional[str] = None
    category: Optional[DishCategory] = None
    status: Optional[DishStatus] = None
    description: Optional[str] = None
    ingredients: Optional[str] = None
    allergens: Optional[str] = None
    price: Optional[Decimal] = None
    cost: Optional[Decimal] = None
    market_price: Optional[Decimal] = None
    member_price: Optional[Decimal] = None
    unit: Optional[str] = None
    serving_size: Optional[str] = None
    weight: Optional[Decimal] = None
    spicy_level: Optional[SpicyLevel] = None
    cooking_method: Optional[CookingMethod] = None
    taste_tags: Optional[str] = None
    prep_time: Optional[int] = None
    cook_time: Optional[int] = None
    difficulty: Optional[int] = None
    kitchen_station: Optional[str] = None
    calories: Optional[int] = None
    protein: Optional[Decimal] = None
    fat: Optional[Decimal] = None
    carbs: Optional[Decimal] = None
    is_recommended: Optional[bool] = None
    is_signature: Optional[bool] = None
    is_new: Optional[bool] = None
    is_popular: Optional[bool] = None
    daily_limit: Optional[int] = None
    min_order_quantity: Optional[int] = None
    max_order_quantity: Optional[int] = None
    available_start_time: Optional[str] = None
    available_end_time: Optional[str] = None
    is_active: Optional[bool] = None
    sort_order: Optional[int] = None


class DishResponse(BaseModel):
    id: int
    name: str
    english_name: Optional[str] = None
    code: Optional[str] = None
    category: DishCategory
    status: DishStatus
    description: Optional[str] = None
    ingredients: Optional[str] = None
    allergens: Optional[str] = None
    price: Decimal
    cost: Optional[Decimal] = None
    market_price: Optional[Decimal] = None
    member_price: Optional[Decimal] = None
    unit: str
    serving_size: Optional[str] = None
    weight: Optional[Decimal] = None
    spicy_level: SpicyLevel
    cooking_method: Optional[CookingMethod] = None
    taste_tags: Optional[str] = None
    prep_time: Optional[int] = None
    cook_time: Optional[int] = None
    total_time: int
    difficulty: Optional[int] = None
    kitchen_station: Optional[str] = None
    calories: Optional[int] = None
    protein: Optional[Decimal] = None
    fat: Optional[Decimal] = None
    carbs: Optional[Decimal] = None
    image_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    is_recommended: bool
    is_signature: bool
    is_new: bool
    is_popular: bool
    daily_limit: Optional[int] = None
    min_order_quantity: int
    max_order_quantity: Optional[int] = None
    available_start_time: Optional[str] = None
    available_end_time: Optional[str] = None
    sales_count: int
    rating: Optional[Decimal] = None
    rating_count: int
    profit_margin: float
    is_available_now: bool
    is_active: bool
    sort_order: int
    created_at: str

    class Config:
        from_attributes = True


class DishListResponse(BaseModel):
    dishes: List[DishResponse]
    total: int
    page: int
    size: int


@router.get("/", response_model=DishListResponse)
async def get_dishes(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    category: Optional[DishCategory] = Query(None),
    status: Optional[DishStatus] = Query(None),
    spicy_level: Optional[SpicyLevel] = Query(None),
    cooking_method: Optional[CookingMethod] = Query(None),
    is_recommended: Optional[bool] = Query(None),
    is_signature: Optional[bool] = Query(None),
    is_new: Optional[bool] = Query(None),
    available_only: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取菜品列表"""
    query = db.query(Dish)
    
    # 搜索过滤
    if search:
        query = query.filter(
            or_(
                Dish.name.ilike(f"%{search}%"),
                Dish.english_name.ilike(f"%{search}%"),
                Dish.code.ilike(f"%{search}%"),
                Dish.ingredients.ilike(f"%{search}%")
            )
        )
    
    # 分类过滤
    if category:
        query = query.filter(Dish.category == category)
    
    # 状态过滤
    if status:
        query = query.filter(Dish.status == status)
    elif available_only:
        query = query.filter(Dish.status == DishStatus.AVAILABLE)
    
    # 辣度过滤
    if spicy_level:
        query = query.filter(Dish.spicy_level == spicy_level)
    
    # 烹饪方式过滤
    if cooking_method:
        query = query.filter(Dish.cooking_method == cooking_method)
    
    # 特色标签过滤
    if is_recommended is not None:
        query = query.filter(Dish.is_recommended == is_recommended)
    
    if is_signature is not None:
        query = query.filter(Dish.is_signature == is_signature)
    
    if is_new is not None:
        query = query.filter(Dish.is_new == is_new)
    
    # 只显示启用的菜品
    query = query.filter(Dish.is_active == True)
    
    # 计算总数
    total = query.count()
    
    # 排序和分页
    dishes = query.order_by(Dish.sort_order, Dish.id).offset((page - 1) * size).limit(size).all()
    
    return DishListResponse(
        dishes=[DishResponse.from_orm(dish) for dish in dishes],
        total=total,
        page=page,
        size=size
    )


@router.post("/", response_model=DishResponse)
async def create_dish(
    dish_data: DishCreate,
    current_user: User = Depends(require_permission("menu.manage")),
    db: Session = Depends(get_db)
):
    """创建菜品"""
    # 检查菜品编码是否已存在
    if dish_data.code and db.query(Dish).filter(Dish.code == dish_data.code).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="菜品编码已存在"
        )
    
    dish = Dish(**dish_data.dict())
    db.add(dish)
    db.commit()
    db.refresh(dish)
    
    return DishResponse.from_orm(dish)


@router.get("/{dish_id}", response_model=DishResponse)
async def get_dish(
    dish_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取菜品详情"""
    dish = db.query(Dish).filter(Dish.id == dish_id).first()
    if not dish:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="菜品不存在"
        )
    
    return DishResponse.from_orm(dish)


@router.put("/{dish_id}", response_model=DishResponse)
async def update_dish(
    dish_id: int,
    dish_data: DishUpdate,
    current_user: User = Depends(require_permission("menu.manage")),
    db: Session = Depends(get_db)
):
    """更新菜品信息"""
    dish = db.query(Dish).filter(Dish.id == dish_id).first()
    if not dish:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="菜品不存在"
        )
    
    # 检查菜品编码是否已被其他菜品使用
    if dish_data.code and dish_data.code != dish.code:
        existing_dish = db.query(Dish).filter(
            Dish.code == dish_data.code,
            Dish.id != dish_id
        ).first()
        if existing_dish:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="菜品编码已被其他菜品使用"
            )
    
    # 更新菜品信息
    update_data = dish_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(dish, field, value)
    
    db.commit()
    db.refresh(dish)
    
    return DishResponse.from_orm(dish)


@router.delete("/{dish_id}")
async def delete_dish(
    dish_id: int,
    current_user: User = Depends(require_permission("menu.manage")),
    db: Session = Depends(get_db)
):
    """删除菜品"""
    dish = db.query(Dish).filter(Dish.id == dish_id).first()
    if not dish:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="菜品不存在"
        )
    
    # 软删除：设置为非激活状态
    dish.is_active = False
    dish.status = DishStatus.DISCONTINUED
    db.commit()
    
    return {"message": "菜品已删除"}


@router.post("/{dish_id}/upload-image")
async def upload_dish_image(
    dish_id: int,
    file: UploadFile = File(...),
    current_user: User = Depends(require_permission("menu.manage")),
    db: Session = Depends(get_db)
):
    """上传菜品图片"""
    dish = db.query(Dish).filter(Dish.id == dish_id).first()
    if not dish:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="菜品不存在"
        )
    
    # 检查文件类型
    if not file.content_type.startswith("image/"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能上传图片文件"
        )
    
    # 检查文件大小
    if file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件大小超过限制"
        )
    
    # 创建上传目录
    upload_dir = Path(settings.UPLOAD_DIR) / "dishes"
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成文件名
    file_extension = file.filename.split(".")[-1]
    filename = f"{uuid.uuid4()}.{file_extension}"
    file_path = upload_dir / filename
    
    # 保存文件
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # 更新菜品图片URL
    image_url = f"/static/uploads/dishes/{filename}"
    dish.image_url = image_url
    dish.thumbnail_url = image_url  # 简化处理，实际应用中可以生成缩略图
    
    db.commit()
    
    return {"message": "图片上传成功", "image_url": image_url}


@router.get("/categories/list")
async def get_dish_categories(
    current_user: User = Depends(get_current_user)
):
    """获取菜品分类列表"""
    categories = [
        {"value": category.value, "label": category.value}
        for category in DishCategory
    ]
    return {"categories": categories}


@router.get("/stats/popular")
async def get_popular_dishes(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取热门菜品"""
    dishes = db.query(Dish).filter(
        Dish.is_active == True,
        Dish.status == DishStatus.AVAILABLE
    ).order_by(Dish.sales_count.desc()).limit(limit).all()
    
    return [
        {
            "id": dish.id,
            "name": dish.name,
            "sales_count": dish.sales_count,
            "price": dish.price,
            "image_url": dish.image_url
        }
        for dish in dishes
    ]
