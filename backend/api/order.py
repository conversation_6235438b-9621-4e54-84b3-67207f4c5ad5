from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from pydantic import BaseModel
from decimal import Decimal
from datetime import datetime, timedelta
import uuid

from core.database import get_db
from core.websocket import broadcast_to_room, broadcast_to_role
from models.order import Order, OrderItem, OrderStatus, OrderType, PaymentMethod, DishItemStatus
from models.table import Table, TableStatus
from models.menu import Dish
from models.user import User
from .auth import get_current_user, require_permission

router = APIRouter()


# Pydantic 模型
class OrderItemCreate(BaseModel):
    dish_id: int
    quantity: int
    special_requirements: Optional[str] = None


class OrderCreate(BaseModel):
    table_id: Optional[int] = None
    order_type: OrderType = OrderType.DINE_IN
    customer_name: Optional[str] = None
    customer_phone: Optional[str] = None
    guest_count: int = 1
    reservation_time: Optional[datetime] = None
    estimated_duration: Optional[int] = None
    special_requests: Optional[str] = None
    notes: Optional[str] = None
    items: List[OrderItemCreate]


class OrderItemUpdate(BaseModel):
    quantity: Optional[int] = None
    special_requirements: Optional[str] = None
    status: Optional[DishItemStatus] = None


class OrderUpdate(BaseModel):
    customer_name: Optional[str] = None
    customer_phone: Optional[str] = None
    guest_count: Optional[int] = None
    reservation_time: Optional[datetime] = None
    estimated_duration: Optional[int] = None
    special_requests: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[OrderStatus] = None


class OrderItemResponse(BaseModel):
    id: int
    dish_id: int
    dish_name: str
    dish_price: Decimal
    dish_category: Optional[str] = None
    quantity: int
    unit_price: Decimal
    total_price: Decimal
    status: DishItemStatus
    assigned_chef_id: Optional[int] = None
    kitchen_notes: Optional[str] = None
    special_requirements: Optional[str] = None
    ordered_at: Optional[datetime] = None
    confirmed_at: Optional[datetime] = None
    started_cooking_at: Optional[datetime] = None
    ready_at: Optional[datetime] = None
    served_at: Optional[datetime] = None
    rush_count: int
    last_rush_at: Optional[datetime] = None
    can_rush: bool
    cooking_duration: int

    class Config:
        from_attributes = True


class OrderResponse(BaseModel):
    id: int
    order_number: str
    order_type: OrderType
    status: OrderStatus
    table_id: Optional[int] = None
    table_number: Optional[str] = None
    customer_id: Optional[int] = None
    waiter_id: Optional[int] = None
    waiter_name: Optional[str] = None
    customer_name: Optional[str] = None
    customer_phone: Optional[str] = None
    guest_count: int
    reservation_time: Optional[datetime] = None
    estimated_duration: Optional[int] = None
    subtotal: Decimal
    service_charge: Decimal
    discount_amount: Decimal
    total_amount: Decimal
    paid_amount: Decimal
    payment_method: Optional[PaymentMethod] = None
    payment_time: Optional[datetime] = None
    special_requests: Optional[str] = None
    notes: Optional[str] = None
    ordered_at: Optional[datetime] = None
    confirmed_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    items: List[OrderItemResponse]
    is_editable: bool
    can_cancel: bool

    class Config:
        from_attributes = True


def generate_order_number() -> str:
    """生成订单号"""
    now = datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")
    random_suffix = str(uuid.uuid4())[:4].upper()
    return f"JY{timestamp}{random_suffix}"


@router.get("/", response_model=List[OrderResponse])
async def get_orders(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[OrderStatus] = Query(None),
    order_type: Optional[OrderType] = Query(None),
    table_id: Optional[int] = Query(None),
    waiter_id: Optional[int] = Query(None),
    date_from: Optional[datetime] = Query(None),
    date_to: Optional[datetime] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取订单列表"""
    query = db.query(Order)
    
    # 权限过滤：服务员只能看到自己负责的订单
    if current_user.role.value == "waiter" and not current_user.has_permission("order.manage"):
        query = query.filter(Order.waiter_id == current_user.id)
    
    # 状态过滤
    if status:
        query = query.filter(Order.status == status)
    
    # 订单类型过滤
    if order_type:
        query = query.filter(Order.order_type == order_type)
    
    # 餐桌过滤
    if table_id:
        query = query.filter(Order.table_id == table_id)
    
    # 服务员过滤
    if waiter_id:
        query = query.filter(Order.waiter_id == waiter_id)
    
    # 日期范围过滤
    if date_from:
        query = query.filter(Order.created_at >= date_from)
    if date_to:
        query = query.filter(Order.created_at <= date_to)
    
    # 排序和分页
    orders = query.order_by(Order.created_at.desc()).offset((page - 1) * size).limit(size).all()
    
    # 构建响应数据
    result = []
    for order in orders:
        # 获取餐桌信息
        table_number = None
        if order.table_id:
            table = db.query(Table).filter(Table.id == order.table_id).first()
            if table:
                table_number = table.number
        
        # 获取服务员信息
        waiter_name = None
        if order.waiter_id:
            waiter = db.query(User).filter(User.id == order.waiter_id).first()
            if waiter:
                waiter_name = waiter.full_name
        
        order_data = OrderResponse.from_orm(order)
        order_data.table_number = table_number
        order_data.waiter_name = waiter_name
        result.append(order_data)
    
    return result


@router.post("/", response_model=OrderResponse)
async def create_order(
    order_data: OrderCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建订单"""
    # 检查餐桌是否可用
    if order_data.table_id:
        table = db.query(Table).filter(Table.id == order_data.table_id).first()
        if not table:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="餐桌不存在"
            )
        
        if not table.is_available and table.status != TableStatus.RESERVED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="餐桌不可用"
            )
        
        # 检查容量
        if not table.can_accommodate(order_data.guest_count):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"餐桌容量不足，最多容纳{table.capacity}人"
            )
    
    # 验证菜品
    dish_ids = [item.dish_id for item in order_data.items]
    dishes = db.query(Dish).filter(Dish.id.in_(dish_ids)).all()
    dish_dict = {dish.id: dish for dish in dishes}
    
    for item in order_data.items:
        if item.dish_id not in dish_dict:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"菜品ID {item.dish_id} 不存在"
            )
        
        dish = dish_dict[item.dish_id]
        if not dish.is_available_now:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"菜品 {dish.name} 当前不可供应"
            )
    
    # 创建订单
    order = Order(
        order_number=generate_order_number(),
        order_type=order_data.order_type,
        table_id=order_data.table_id,
        waiter_id=current_user.id,
        customer_name=order_data.customer_name,
        customer_phone=order_data.customer_phone,
        guest_count=order_data.guest_count,
        reservation_time=order_data.reservation_time,
        estimated_duration=order_data.estimated_duration,
        special_requests=order_data.special_requests,
        notes=order_data.notes,
        ordered_at=datetime.utcnow()
    )
    
    db.add(order)
    db.flush()  # 获取订单ID
    
    # 创建订单项
    total_amount = Decimal('0')
    for item_data in order_data.items:
        dish = dish_dict[item_data.dish_id]
        
        order_item = OrderItem(
            order_id=order.id,
            dish_id=dish.id,
            dish_name=dish.name,
            dish_price=dish.price,
            dish_category=dish.category.value,
            quantity=item_data.quantity,
            unit_price=dish.price,
            total_price=dish.price * item_data.quantity,
            special_requirements=item_data.special_requirements,
            ordered_at=datetime.utcnow()
        )
        
        db.add(order_item)
        total_amount += order_item.total_price
    
    # 计算订单总金额
    order.subtotal = total_amount
    if order.table_id:
        table = db.query(Table).filter(Table.id == order.table_id).first()
        if table:
            order.service_charge = table.get_service_charge(total_amount)
    
    order.total_amount = order.subtotal + order.service_charge - order.discount_amount
    
    # 更新餐桌状态
    if order.table_id:
        table = db.query(Table).filter(Table.id == order.table_id).first()
        if table:
            if order_data.reservation_time and order_data.reservation_time > datetime.utcnow():
                table.status = TableStatus.RESERVED
            else:
                table.status = TableStatus.OCCUPIED
                table.occupied_since = datetime.utcnow()
            
            table.current_guests = order_data.guest_count
            table.estimated_duration = order_data.estimated_duration
            table.assigned_waiter_id = current_user.id
    
    db.commit()
    db.refresh(order)
    
    # 发送实时通知
    await broadcast_to_room("kitchen", "new_order", {
        "order_id": order.id,
        "order_number": order.order_number,
        "table_number": table.number if table else None,
        "items_count": len(order_data.items),
        "message": f"新订单：{order.order_number}"
    })
    
    return OrderResponse.from_orm(order)


@router.get("/{order_id}", response_model=OrderResponse)
async def get_order(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取订单详情"""
    order = db.query(Order).filter(Order.id == order_id).first()
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    # 权限检查：服务员只能查看自己负责的订单
    if (current_user.role.value == "waiter" and 
        not current_user.has_permission("order.manage") and 
        order.waiter_id != current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看此订单"
        )
    
    return OrderResponse.from_orm(order)


@router.put("/{order_id}", response_model=OrderResponse)
async def update_order(
    order_id: int,
    order_data: OrderUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新订单信息"""
    order = db.query(Order).filter(Order.id == order_id).first()
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    # 权限检查
    if (current_user.role.value == "waiter" and 
        not current_user.has_permission("order.manage") and 
        order.waiter_id != current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此订单"
        )
    
    # 检查订单是否可编辑
    if not order.is_editable:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="订单当前状态不允许修改"
        )
    
    # 更新订单信息
    update_data = order_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(order, field, value)
    
    # 如果状态发生变化，记录时间
    if order_data.status:
        now = datetime.utcnow()
        if order_data.status == OrderStatus.CONFIRMED:
            order.confirmed_at = now
        elif order_data.status == OrderStatus.IN_PROGRESS:
            order.started_at = now
        elif order_data.status == OrderStatus.COMPLETED:
            order.completed_at = now
    
    db.commit()
    db.refresh(order)
    
    # 发送状态更新通知
    if order_data.status:
        await broadcast_to_room("management", "order_status_changed", {
            "order_id": order.id,
            "status": order.status.value,
            "table_id": order.table_id
        })
    
    return OrderResponse.from_orm(order)


@router.post("/{order_id}/items", response_model=OrderItemResponse)
async def add_order_item(
    order_id: int,
    item_data: OrderItemCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """添加订单项"""
    order = db.query(Order).filter(Order.id == order_id).first()
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )

    # 权限检查
    if (current_user.role.value == "waiter" and
        not current_user.has_permission("order.manage") and
        order.waiter_id != current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此订单"
        )

    # 检查订单是否可编辑
    if not order.is_editable:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="订单当前状态不允许添加菜品"
        )

    # 验证菜品
    dish = db.query(Dish).filter(Dish.id == item_data.dish_id).first()
    if not dish:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="菜品不存在"
        )

    if not dish.is_available_now:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"菜品 {dish.name} 当前不可供应"
        )

    # 创建订单项
    order_item = OrderItem(
        order_id=order.id,
        dish_id=dish.id,
        dish_name=dish.name,
        dish_price=dish.price,
        dish_category=dish.category.value,
        quantity=item_data.quantity,
        unit_price=dish.price,
        total_price=dish.price * item_data.quantity,
        special_requirements=item_data.special_requirements,
        ordered_at=datetime.utcnow()
    )

    db.add(order_item)

    # 重新计算订单总金额
    order.calculate_total()

    db.commit()
    db.refresh(order_item)

    # 发送实时通知
    await broadcast_to_room("kitchen", "new_dish_item", {
        "order_id": order.id,
        "dish_name": dish.name,
        "quantity": item_data.quantity,
        "table_number": order.table.number if order.table else None
    })

    return OrderItemResponse.from_orm(order_item)


@router.put("/{order_id}/items/{item_id}", response_model=OrderItemResponse)
async def update_order_item(
    order_id: int,
    item_id: int,
    item_data: OrderItemUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新订单项"""
    order_item = db.query(OrderItem).filter(
        OrderItem.id == item_id,
        OrderItem.order_id == order_id
    ).first()

    if not order_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单项不存在"
        )

    order = order_item.order

    # 权限检查
    if (current_user.role.value == "waiter" and
        not current_user.has_permission("order.manage") and
        order.waiter_id != current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此订单项"
        )

    # 更新订单项
    update_data = item_data.dict(exclude_unset=True)

    # 如果更新数量，重新计算价格
    if 'quantity' in update_data:
        order_item.quantity = update_data['quantity']
        order_item.total_price = order_item.unit_price * order_item.quantity

    # 更新状态
    if 'status' in update_data:
        order_item.update_status(update_data['status'])

    # 更新其他字段
    for field, value in update_data.items():
        if field not in ['quantity', 'status']:
            setattr(order_item, field, value)

    # 重新计算订单总金额
    order.calculate_total()

    db.commit()
    db.refresh(order_item)

    # 发送状态更新通知
    if 'status' in update_data:
        await broadcast_to_room("management", "dish_status_changed", {
            "order_id": order.id,
            "dish_id": order_item.dish_id,
            "status": order_item.status.value,
            "table_id": order.table_id
        })

    return OrderItemResponse.from_orm(order_item)


@router.delete("/{order_id}/items/{item_id}")
async def delete_order_item(
    order_id: int,
    item_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除订单项"""
    order_item = db.query(OrderItem).filter(
        OrderItem.id == item_id,
        OrderItem.order_id == order_id
    ).first()

    if not order_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单项不存在"
        )

    order = order_item.order

    # 权限检查
    if (current_user.role.value == "waiter" and
        not current_user.has_permission("order.manage") and
        order.waiter_id != current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此订单项"
        )

    # 检查是否可以删除
    if order_item.status in [DishItemStatus.COOKING, DishItemStatus.READY, DishItemStatus.SERVED]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="菜品已开始制作或已完成，无法删除"
        )

    # 标记为已取消
    order_item.status = DishItemStatus.CANCELLED

    # 重新计算订单总金额
    order.calculate_total()

    db.commit()

    return {"message": "订单项已删除"}


@router.post("/{order_id}/items/{item_id}/rush")
async def rush_dish(
    order_id: int,
    item_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """催菜"""
    order_item = db.query(OrderItem).filter(
        OrderItem.id == item_id,
        OrderItem.order_id == order_id
    ).first()

    if not order_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单项不存在"
        )

    # 检查是否可以催菜
    if not order_item.can_rush:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前无法催菜，请稍后再试"
        )

    # 更新催菜信息
    order_item.rush_count += 1
    order_item.last_rush_at = datetime.utcnow()

    db.commit()

    # 发送催菜通知
    await broadcast_to_room("kitchen", "dish_rush", {
        "order_id": order_id,
        "dish_name": order_item.dish_name,
        "table_number": order_item.order.table.number if order_item.order.table else None,
        "rush_count": order_item.rush_count,
        "message": f"催菜提醒：{order_item.dish_name}"
    })

    return {"message": "催菜成功", "rush_count": order_item.rush_count}
