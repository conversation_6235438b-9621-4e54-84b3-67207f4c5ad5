import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Image,
  Upload,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  UploadOutlined,
  StarOutlined,
  FireOutlined,
  GiftOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { UploadProps } from 'antd';
import MainLayout from '@/components/Layout/MainLayout';
import { useAuthStore } from '@/store';
import { menuApi } from '@/api';
import { Dish, DishFormData, DishCategory, DishStatus, SpicyLevel, CookingMethod } from '@/types';
import {
  DISH_CATEGORY_LABELS,
  DISH_STATUS_LABELS,
  DISH_STATUS_COLORS,
  SPICY_LEVEL_LABELS,
  COOKING_METHOD_LABELS,
} from '@/utils/constants';
import { formatCurrency } from '@/utils/helpers';

const { Option } = Select;
const { TextArea } = Input;

const MenuPage: React.FC = () => {
  const { user } = useAuthStore();
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDish, setEditingDish] = useState<Dish | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({
    search: '',
    category: undefined as DishCategory | undefined,
    status: undefined as DishStatus | undefined,
  });
  const [form] = Form.useForm();

  // 加载菜品列表
  const loadDishes = async (page = 1, size = 20) => {
    try {
      setLoading(true);
      const params = {
        page,
        size,
        ...filters,
      };
      const data = await menuApi.getDishes(params);
      setDishes(data.dishes);
      setPagination({
        current: page,
        pageSize: size,
        total: data.total,
      });
    } catch (error) {
      message.error('加载菜品列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDishes();
  }, [filters]);

  // 处理创建/编辑菜品
  const handleSubmit = async (values: DishFormData) => {
    try {
      if (editingDish) {
        await menuApi.updateDish(editingDish.id, values);
        message.success('菜品信息更新成功');
      } else {
        await menuApi.createDish(values);
        message.success('菜品创建成功');
      }
      setModalVisible(false);
      setEditingDish(null);
      form.resetFields();
      loadDishes(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error(editingDish ? '更新菜品失败' : '创建菜品失败');
    }
  };

  // 处理删除菜品
  const handleDelete = async (id: number) => {
    try {
      await menuApi.deleteDish(id);
      message.success('菜品删除成功');
      loadDishes(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('删除菜品失败');
    }
  };

  // 处理图片上传
  const handleImageUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;
    
    if (!editingDish) {
      onError?.(new Error('请先保存菜品信息'));
      return;
    }

    try {
      const result = await menuApi.uploadDishImage(editingDish.id, file as File);
      message.success('图片上传成功');
      onSuccess?.(result);
      loadDishes(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('图片上传失败');
      onError?.(error as Error);
    }
  };

  // 打开编辑模态框
  const openEditModal = (dish?: Dish) => {
    setEditingDish(dish || null);
    setModalVisible(true);
    
    if (dish) {
      form.setFieldsValue({
        ...dish,
        category: dish.category,
        spicy_level: dish.spicy_level,
        cooking_method: dish.cooking_method,
      });
    } else {
      form.resetFields();
    }
  };

  // 表格列定义
  const columns: ColumnsType<Dish> = [
    {
      title: '图片',
      dataIndex: 'image_url',
      key: 'image',
      width: 80,
      render: (imageUrl) => (
        <Image
          width={60}
          height={60}
          src={imageUrl || '/placeholder-dish.png'}
          fallback="/placeholder-dish.png"
          style={{ objectFit: 'cover', borderRadius: 4 }}
        />
      ),
    },
    {
      title: '菜品信息',
      key: 'info',
      width: 200,
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.name}</div>
          {record.english_name && (
            <div className="text-sm text-gray-500">{record.english_name}</div>
          )}
          <div className="text-xs text-gray-400">编码: {record.code || '无'}</div>
          <Space size="small" className="mt-1">
            {record.is_signature && <Tag color="gold" icon={<StarOutlined />}>招牌</Tag>}
            {record.is_recommended && <Tag color="blue">推荐</Tag>}
            {record.is_new && <Tag color="green" icon={<GiftOutlined />}>新品</Tag>}
            {record.is_popular && <Tag color="red" icon={<FireOutlined />}>热门</Tag>}
          </Space>
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => (
        <Tag color="blue">{DISH_CATEGORY_LABELS[category]}</Tag>
      ),
    },
    {
      title: '价格',
      key: 'price',
      width: 120,
      render: (_, record) => (
        <div>
          <div className="font-medium text-lg">{formatCurrency(record.price)}</div>
          {record.member_price && record.member_price !== record.price && (
            <div className="text-sm text-orange-500">
              会员价: {formatCurrency(record.member_price)}
            </div>
          )}
          {record.cost && (
            <div className="text-xs text-gray-500">
              成本: {formatCurrency(record.cost)}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={DISH_STATUS_COLORS[status]}>
          {DISH_STATUS_LABELS[status]}
        </Tag>
      ),
    },
    {
      title: '口味',
      key: 'taste',
      width: 120,
      render: (_, record) => (
        <div>
          <Tag color={record.spicy_level === SpicyLevel.NONE ? 'default' : 'red'}>
            {SPICY_LEVEL_LABELS[record.spicy_level]}
          </Tag>
          {record.cooking_method && (
            <Tag color="green" className="mt-1">
              {COOKING_METHOD_LABELS[record.cooking_method]}
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '制作时间',
      key: 'time',
      width: 100,
      render: (_, record) => (
        <div className="text-sm">
          {record.total_time > 0 ? `${record.total_time}分钟` : '-'}
          {record.prep_time && record.cook_time && (
            <div className="text-xs text-gray-500">
              备菜{record.prep_time}分 + 烹饪{record.cook_time}分
            </div>
          )}
        </div>
      ),
    },
    {
      title: '销量',
      dataIndex: 'sales_count',
      key: 'sales_count',
      width: 80,
      render: (count) => (
        <div className="text-center">
          <div className="font-medium">{count}</div>
          <div className="text-xs text-gray-500">份</div>
        </div>
      ),
    },
    {
      title: '利润率',
      key: 'profit',
      width: 100,
      render: (_, record) => (
        <div>
          <Progress
            percent={record.profit_margin}
            size="small"
            format={(percent) => `${percent}%`}
            strokeColor={
              record.profit_margin >= 60 ? '#52c41a' :
              record.profit_margin >= 40 ? '#faad14' : '#f5222d'
            }
          />
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => openEditModal(record)}
            />
          </Tooltip>
          
          {user?.has_permission?.('menu.manage') && (
            <>
              <Tooltip title="编辑">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => openEditModal(record)}
                />
              </Tooltip>
              
              <Popconfirm
                title="确定要删除这个菜品吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Tooltip title="删除">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                  />
                </Tooltip>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面头部 */}
        <Card>
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold mb-2">菜单管理</h2>
              <p className="text-gray-600">管理酒店的菜品信息和价格</p>
            </div>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => loadDishes(pagination.current, pagination.pageSize)}
                loading={loading}
              >
                刷新
              </Button>
              
              {user?.has_permission?.('menu.manage') && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => openEditModal()}
                >
                  新增菜品
                </Button>
              )}
            </Space>
          </div>
        </Card>

        {/* 筛选器 */}
        <Card>
          <Row gutter={16}>
            <Col span={8}>
              <Input.Search
                placeholder="搜索菜品名称、编码"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                onSearch={() => loadDishes(1, pagination.pageSize)}
                allowClear
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="选择分类"
                value={filters.category}
                onChange={(value) => setFilters({ ...filters, category: value })}
                allowClear
                style={{ width: '100%' }}
              >
                {Object.entries(DISH_CATEGORY_LABELS).map(([value, label]) => (
                  <Option key={value} value={value}>
                    {label}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                placeholder="选择状态"
                value={filters.status}
                onChange={(value) => setFilters({ ...filters, status: value })}
                allowClear
                style={{ width: '100%' }}
              >
                {Object.entries(DISH_STATUS_LABELS).map(([value, label]) => (
                  <Option key={value} value={value}>
                    {label}
                  </Option>
                ))}
              </Select>
            </Col>
          </Row>
        </Card>

        {/* 菜品列表 */}
        <Card>
          <Table
            columns={columns}
            dataSource={dishes}
            rowKey="id"
            loading={loading}
            scroll={{ x: 1400 }}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个菜品`,
              onChange: (page, size) => {
                loadDishes(page, size);
              },
            }}
          />
        </Card>
      </div>

      {/* 创建/编辑菜品模态框 */}
      <Modal
        title={editingDish ? '编辑菜品' : '新增菜品'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingDish(null);
          form.resetFields();
        }}
        footer={null}
        width={900}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            category: DishCategory.HOT_DISH,
            status: DishStatus.AVAILABLE,
            spicy_level: SpicyLevel.NONE,
            unit: '份',
            min_order_quantity: 1,
            sort_order: 0,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="菜品名称"
                rules={[{ required: true, message: '请输入菜品名称' }]}
              >
                <Input placeholder="如：宫保鸡丁" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="english_name" label="英文名称">
                <Input placeholder="如：Kung Pao Chicken" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="code" label="菜品编码">
                <Input placeholder="如：HD001" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择菜品分类' }]}
              >
                <Select>
                  {Object.entries(DISH_CATEGORY_LABELS).map(([value, label]) => (
                    <Option key={value} value={value}>
                      {label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="status" label="状态">
                <Select>
                  {Object.entries(DISH_STATUS_LABELS).map(([value, label]) => (
                    <Option key={value} value={value}>
                      {label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="菜品描述">
            <TextArea rows={2} placeholder="菜品的详细描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="ingredients" label="主要食材">
                <TextArea rows={2} placeholder="主要食材列表" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="allergens" label="过敏原信息">
                <TextArea rows={2} placeholder="过敏原提示" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="cost" label="成本">
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="member_price" label="会员价">
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="unit" label="单位">
                <Input placeholder="如：份、斤、个" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="spicy_level" label="辣度">
                <Select>
                  {Object.entries(SPICY_LEVEL_LABELS).map(([value, label]) => (
                    <Option key={value} value={value}>
                      {label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="cooking_method" label="烹饪方式">
                <Select allowClear>
                  {Object.entries(COOKING_METHOD_LABELS).map(([value, label]) => (
                    <Option key={value} value={value}>
                      {label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="kitchen_station" label="制作工位">
                <Input placeholder="如：热菜档、凉菜档" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="prep_time" label="准备时间(分钟)">
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="cook_time" label="烹饪时间(分钟)">
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="difficulty" label="制作难度(1-5)">
                <InputNumber min={1} max={5} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <div className="mb-4">
            <h4 className="mb-3">特色标签</h4>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item name="is_signature" valuePropName="checked">
                  <Switch checkedChildren="招牌菜" unCheckedChildren="招牌菜" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="is_recommended" valuePropName="checked">
                  <Switch checkedChildren="推荐" unCheckedChildren="推荐" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="is_new" valuePropName="checked">
                  <Switch checkedChildren="新品" unCheckedChildren="新品" />
                </Form.Item>
              </Col>
            </Row>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="daily_limit" label="每日限量">
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="min_order_quantity" label="最小起订量">
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="sort_order" label="排序">
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          {editingDish && (
            <Form.Item label="菜品图片">
              <Upload
                customRequest={handleImageUpload}
                showUploadList={false}
                accept="image/*"
              >
                <Button icon={<UploadOutlined />}>上传图片</Button>
              </Upload>
              {editingDish.image_url && (
                <div className="mt-2">
                  <Image
                    width={100}
                    height={100}
                    src={editingDish.image_url}
                    style={{ objectFit: 'cover', borderRadius: 4 }}
                  />
                </div>
              )}
            </Form.Item>
          )}

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingDish ? '更新' : '创建'}
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setEditingDish(null);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </MainLayout>
  );
};

export default MenuPage;
