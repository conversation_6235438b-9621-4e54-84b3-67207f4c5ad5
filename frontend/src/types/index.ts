// 用户相关类型
export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  DEPUTY_MANAGER = 'deputy_manager',
  WAITER = 'waiter',
  CHEF_MANAGER = 'chef_manager',
  CHEF = 'chef',
  KITCHEN_HELPER = 'kitchen_helper',
  CASHIER = 'cashier',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

export interface User {
  id: number;
  username: string;
  full_name: string;
  role: UserRole;
  status: UserStatus;
  email?: string;
  phone?: string;
  employee_id?: string;
  department?: string;
  position?: string;
  avatar_url?: string;
  is_active: boolean;
  created_at: string;
  last_login_at?: string;
  is_management?: boolean;
  is_kitchen_staff?: boolean;
  is_service_staff?: boolean;
}

// 餐桌相关类型
export enum TableType {
  PRIVATE_ROOM = 'private_room',
  HALL_TABLE = 'hall_table',
  VIP_ROOM = 'vip_room',
  OUTDOOR = 'outdoor',
}

export enum TableStatus {
  AVAILABLE = 'available',
  RESERVED = 'reserved',
  OCCUPIED = 'occupied',
  CLEANING = 'cleaning',
  MAINTENANCE = 'maintenance',
  OUT_OF_SERVICE = 'out_of_service',
}

export interface Table {
  id: number;
  number: string;
  name?: string;
  table_type: TableType;
  status: TableStatus;
  capacity: number;
  min_capacity?: number;
  floor?: string;
  area?: string;
  location_description?: string;
  facilities_list: string[];
  minimum_charge?: number;
  service_charge_rate?: number;
  hourly_rate?: number;
  is_vip_only: boolean;
  requires_reservation: boolean;
  current_guests: number;
  occupied_since?: string;
  estimated_duration?: number;
  assigned_waiter_id?: number;
  is_active: boolean;
  notes?: string;
  created_at: string;
}

// 菜品相关类型
export enum DishCategory {
  COLD_DISH = 'cold_dish',
  HOT_DISH = 'hot_dish',
  SOUP = 'soup',
  STAPLE = 'staple',
  DESSERT = 'dessert',
  BEVERAGE = 'beverage',
  ALCOHOL = 'alcohol',
  SEAFOOD = 'seafood',
  VEGETARIAN = 'vegetarian',
  SPECIALTY = 'specialty',
}

export enum DishStatus {
  AVAILABLE = 'available',
  UNAVAILABLE = 'unavailable',
  SEASONAL = 'seasonal',
  LIMITED = 'limited',
  DISCONTINUED = 'discontinued',
}

export enum SpicyLevel {
  NONE = 'none',
  MILD = 'mild',
  MEDIUM = 'medium',
  HOT = 'hot',
  EXTRA_HOT = 'extra_hot',
}

export enum CookingMethod {
  STIR_FRY = 'stir_fry',
  STEAM = 'steam',
  BOIL = 'boil',
  BRAISE = 'braise',
  GRILL = 'grill',
  DEEP_FRY = 'deep_fry',
  COLD_MIX = 'cold_mix',
  STEW = 'stew',
  ROAST = 'roast',
}

export interface Dish {
  id: number;
  name: string;
  english_name?: string;
  code?: string;
  category: DishCategory;
  status: DishStatus;
  description?: string;
  ingredients?: string;
  allergens?: string;
  price: number;
  cost?: number;
  market_price?: number;
  member_price?: number;
  unit: string;
  serving_size?: string;
  weight?: number;
  spicy_level: SpicyLevel;
  cooking_method?: CookingMethod;
  taste_tags?: string;
  prep_time?: number;
  cook_time?: number;
  total_time: number;
  difficulty?: number;
  kitchen_station?: string;
  calories?: number;
  protein?: number;
  fat?: number;
  carbs?: number;
  image_url?: string;
  thumbnail_url?: string;
  is_recommended: boolean;
  is_signature: boolean;
  is_new: boolean;
  is_popular: boolean;
  daily_limit?: number;
  min_order_quantity: number;
  max_order_quantity?: number;
  available_start_time?: string;
  available_end_time?: string;
  sales_count: number;
  rating?: number;
  rating_count: number;
  profit_margin: number;
  is_available_now: boolean;
  is_active: boolean;
  sort_order: number;
  created_at: string;
}

// 订单相关类型
export enum OrderStatus {
  DRAFT = 'draft',
  PENDING_KITCHEN = 'pending_kitchen',
  CONFIRMED = 'confirmed',
  PENDING_START = 'pending_start',
  IN_PROGRESS = 'in_progress',
  PENDING_PAYMENT = 'pending_payment',
  PAID = 'paid',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum OrderType {
  DINE_IN = 'dine_in',
  TAKEAWAY = 'takeaway',
  DELIVERY = 'delivery',
  RESERVATION = 'reservation',
}

export enum PaymentMethod {
  CASH = 'cash',
  CARD = 'card',
  WECHAT = 'wechat',
  ALIPAY = 'alipay',
  MEMBER_CARD = 'member_card',
  VOUCHER = 'voucher',
}

export enum DishItemStatus {
  PENDING_CONFIRM = 'pending_confirm',
  CONFIRMED = 'confirmed',
  PENDING_COOK = 'pending_cook',
  COOKING = 'cooking',
  READY = 'ready',
  SERVED = 'served',
  CANCELLED = 'cancelled',
}

export interface OrderItem {
  id: number;
  dish_id: number;
  dish_name: string;
  dish_price: number;
  dish_category?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  status: DishItemStatus;
  assigned_chef_id?: number;
  kitchen_notes?: string;
  special_requirements?: string;
  ordered_at?: string;
  confirmed_at?: string;
  started_cooking_at?: string;
  ready_at?: string;
  served_at?: string;
  rush_count: number;
  last_rush_at?: string;
  can_rush: boolean;
  cooking_duration: number;
}

export interface Order {
  id: number;
  order_number: string;
  order_type: OrderType;
  status: OrderStatus;
  table_id?: number;
  table_number?: string;
  customer_id?: number;
  waiter_id?: number;
  waiter_name?: string;
  customer_name?: string;
  customer_phone?: string;
  guest_count: number;
  reservation_time?: string;
  estimated_duration?: number;
  subtotal: number;
  service_charge: number;
  discount_amount: number;
  total_amount: number;
  paid_amount: number;
  payment_method?: PaymentMethod;
  payment_time?: string;
  special_requests?: string;
  notes?: string;
  ordered_at?: string;
  confirmed_at?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
  items: OrderItem[];
  is_editable: boolean;
  can_cancel: boolean;
}

// API 响应类型
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  success?: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp?: number;
}

// 登录相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
}

// 表单类型
export interface TableFormData {
  number: string;
  name?: string;
  table_type: TableType;
  capacity: number;
  min_capacity?: number;
  floor?: string;
  area?: string;
  location_description?: string;
  has_tv?: boolean;
  has_karaoke?: boolean;
  has_mahjong?: boolean;
  has_projector?: boolean;
  has_wifi?: boolean;
  has_air_conditioning?: boolean;
  minimum_charge?: number;
  service_charge_rate?: number;
  hourly_rate?: number;
  is_vip_only?: boolean;
  requires_reservation?: boolean;
  notes?: string;
}

export interface DishFormData {
  name: string;
  english_name?: string;
  code?: string;
  category: DishCategory;
  description?: string;
  ingredients?: string;
  allergens?: string;
  price: number;
  cost?: number;
  market_price?: number;
  member_price?: number;
  unit?: string;
  serving_size?: string;
  weight?: number;
  spicy_level?: SpicyLevel;
  cooking_method?: CookingMethod;
  taste_tags?: string;
  prep_time?: number;
  cook_time?: number;
  difficulty?: number;
  kitchen_station?: string;
  calories?: number;
  protein?: number;
  fat?: number;
  carbs?: number;
  is_recommended?: boolean;
  is_signature?: boolean;
  is_new?: boolean;
  daily_limit?: number;
  min_order_quantity?: number;
  max_order_quantity?: number;
  available_start_time?: string;
  available_end_time?: string;
  sort_order?: number;
}
