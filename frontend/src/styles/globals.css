@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', 'SimHei', sans-serif;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ant Design 样式覆盖 */
.ant-layout {
  background: #f5f5f5;
}

.ant-layout-header {
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.ant-menu-dark {
  background: #001529;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #f2750a !important;
}

.ant-menu-dark .ant-menu-item:hover {
  background-color: rgba(242, 117, 10, 0.1) !important;
}

/* 卡片样式 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24px;
}

.ant-card-body {
  padding: 24px;
}

/* 按钮样式 */
.ant-btn-primary {
  background: #f2750a;
  border-color: #f2750a;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background: #e35d05;
  border-color: #e35d05;
}

/* 表格样式 */
.ant-table {
  border-radius: 8px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-input,
.ant-input-password,
.ant-select-selector,
.ant-picker {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused,
.ant-input-password:focus,
.ant-input-password-focused,
.ant-select-focused .ant-select-selector,
.ant-picker-focused {
  border-color: #f2750a;
  box-shadow: 0 0 0 2px rgba(242, 117, 10, 0.1);
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 统计数字样式 */
.ant-statistic-content {
  font-weight: 600;
}

/* 进度条样式 */
.ant-progress-bg {
  background: linear-gradient(90deg, #f2750a 0%, #faad14 100%);
}

/* 消息提示样式 */
.ant-message {
  z-index: 9999;
}

.ant-message-notice-content {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 抽屉样式 */
.ant-drawer-content {
  border-radius: 8px 0 0 8px;
}

/* 模态框样式 */
.ant-modal-content {
  border-radius: 8px;
}

.ant-modal-header {
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #f0f0f0;
}

/* 下拉菜单样式 */
.ant-dropdown {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 工具提示样式 */
.ant-tooltip-inner {
  border-radius: 6px;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .ant-layout-header {
    padding: 0 16px;
  }
  
  .ant-card-body {
    padding: 16px;
  }
  
  .ant-table-wrapper {
    overflow-x: auto;
  }
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

/* 加载状态 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .ant-layout-sider {
    display: none !important;
  }
  
  .ant-layout-header {
    display: none !important;
  }
  
  .ant-card {
    box-shadow: none;
    border: 1px solid #d9d9d9;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ant-card {
    border: 2px solid #000;
  }
  
  .ant-btn {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
