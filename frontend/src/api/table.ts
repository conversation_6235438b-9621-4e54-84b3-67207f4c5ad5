import { api } from './client';
import { Table, TableFormData, TableStatus } from '@/types';

export const tableApi = {
  // 获取餐桌列表
  getTables: (params?: {
    table_type?: string;
    status?: TableStatus;
    floor?: string;
    area?: string;
    available_only?: boolean;
  }): Promise<Table[]> => {
    return api.get('/api/tables', { params });
  },
  
  // 创建餐桌
  createTable: (data: TableFormData): Promise<Table> => {
    return api.post('/api/tables', data);
  },
  
  // 获取餐桌详情
  getTable: (id: number): Promise<Table> => {
    return api.get(`/api/tables/${id}`);
  },
  
  // 更新餐桌信息
  updateTable: (id: number, data: Partial<TableFormData>): Promise<Table> => {
    return api.put(`/api/tables/${id}`, data);
  },
  
  // 更新餐桌状态
  updateTableStatus: (id: number, data: {
    status: TableStatus;
    current_guests?: number;
    estimated_duration?: number;
    assigned_waiter_id?: number;
    special_requirements?: string;
  }): Promise<Table> => {
    return api.patch(`/api/tables/${id}/status`, data);
  },
  
  // 删除餐桌
  deleteTable: (id: number): Promise<{ message: string }> => {
    return api.delete(`/api/tables/${id}`);
  },
  
  // 获取餐桌统计
  getTableStats: (): Promise<{
    total_tables: number;
    available_tables: number;
    occupied_tables: number;
    reserved_tables: number;
    occupancy_rate: number;
    type_stats: Record<string, number>;
  }> => {
    return api.get('/api/tables/stats/overview');
  },
};
