import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Typography,
  Space,
  Badge,
  Button,
  Drawer,
  message,
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  TableOutlined,
  BookOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  SettingOutlined,
  LogoutOutlined,
  UserOutlined,
  BellOutlined,
  MenuOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '@/store';
import { UserRole } from '@/types';
import { USER_ROLE_LABELS } from '@/utils/constants';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const router = useRouter();
  const { user, logout, isAuthenticated } = useAuthStore();
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检查是否为移动设备
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 检查认证状态
  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/login');
    }
  }, [isAuthenticated, router]);

  // 根据用户角色生成菜单项
  const getMenuItems = () => {
    if (!user) return [];

    const baseItems = [
      {
        key: '/',
        icon: <DashboardOutlined />,
        label: '工作台',
      },
    ];

    // 管理端菜单
    if (user.is_management) {
      return [
        ...baseItems,
        {
          key: '/tables',
          icon: <TableOutlined />,
          label: '餐桌管理',
        },
        {
          key: '/menu',
          icon: <BookOutlined />,
          label: '菜单管理',
        },
        {
          key: '/orders',
          icon: <ShoppingCartOutlined />,
          label: '订单管理',
        },
        {
          key: '/users',
          icon: <TeamOutlined />,
          label: '用户管理',
        },
        {
          key: '/settings',
          icon: <SettingOutlined />,
          label: '系统设置',
        },
      ];
    }

    // 服务员菜单
    if (user.is_service_staff) {
      return [
        ...baseItems,
        {
          key: '/waiter/tables',
          icon: <TableOutlined />,
          label: '餐桌状态',
        },
        {
          key: '/waiter/orders',
          icon: <ShoppingCartOutlined />,
          label: '我的订单',
        },
        {
          key: '/menu',
          icon: <BookOutlined />,
          label: '菜单浏览',
        },
      ];
    }

    // 厨房菜单
    if (user.is_kitchen_staff) {
      return [
        ...baseItems,
        {
          key: '/kitchen/orders',
          icon: <ShoppingCartOutlined />,
          label: '订单管理',
        },
        {
          key: '/kitchen/dishes',
          icon: <BookOutlined />,
          label: '菜品制作',
        },
      ];
    }

    return baseItems;
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    router.push(key);
    if (isMobile) {
      setMobileMenuVisible(false);
    }
  };

  const handleLogout = () => {
    logout();
    message.success('已退出登录');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => router.push('/profile'),
    },
    {
      key: 'divider',
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  if (!user) {
    return null; // 或者显示加载状态
  }

  const siderContent = (
    <>
      {/* Logo */}
      <div className="h-16 flex items-center justify-center bg-jiyang-600 text-white">
        {collapsed ? (
          <span className="text-xl font-bold">暨</span>
        ) : (
          <span className="text-lg font-bold">暨阳湖大酒店</span>
        )}
      </div>

      {/* 菜单 */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[router.pathname]}
        items={getMenuItems()}
        onClick={handleMenuClick}
        className="border-r-0"
      />
    </>
  );

  return (
    <Layout className="min-h-screen">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          className="shadow-lg"
          theme="dark"
        >
          {siderContent}
        </Sider>
      )}

      {/* 移动端抽屉菜单 */}
      {isMobile && (
        <Drawer
          title="菜单"
          placement="left"
          onClose={() => setMobileMenuVisible(false)}
          open={mobileMenuVisible}
          bodyStyle={{ padding: 0 }}
          width={250}
        >
          <div className="bg-gray-800 text-white min-h-full">
            {siderContent}
          </div>
        </Drawer>
      )}

      <Layout>
        {/* 头部 */}
        <Header className="bg-white shadow-sm px-4 flex items-center justify-between">
          <div className="flex items-center">
            {isMobile ? (
              <Button
                type="text"
                icon={<MenuOutlined />}
                onClick={() => setMobileMenuVisible(true)}
                className="mr-4"
              />
            ) : (
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                className="mr-4"
              />
            )}
            
            <Text strong className="text-lg">
              {getMenuItems().find(item => item.key === router.pathname)?.label || '工作台'}
            </Text>
          </div>

          <Space size="middle">
            {/* 通知 */}
            <Badge count={0} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                className="flex items-center justify-center"
              />
            </Badge>

            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <div className="flex items-center cursor-pointer hover:bg-gray-50 px-2 py-1 rounded">
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  src={user.avatar_url}
                  className="mr-2"
                />
                <div className="hidden sm:block">
                  <div className="text-sm font-medium">{user.full_name}</div>
                  <div className="text-xs text-gray-500">
                    {USER_ROLE_LABELS[user.role]}
                  </div>
                </div>
              </div>
            </Dropdown>
          </Space>
        </Header>

        {/* 内容区域 */}
        <Content className="p-6 bg-gray-50 overflow-auto">
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
