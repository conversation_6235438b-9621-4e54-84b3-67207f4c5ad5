{"name": "jiyang-hotel-frontend", "version": "1.0.0", "description": "暨阳湖大酒店传菜管理系统前端", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "zustand": "^4.4.0", "axios": "^1.6.0", "socket.io-client": "^4.7.0", "dayjs": "^1.11.0", "classnames": "^2.3.0", "react-query": "^3.39.0", "js-cookie": "^3.0.0", "@types/js-cookie": "^3.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "engines": {"node": ">=18.0.0"}}